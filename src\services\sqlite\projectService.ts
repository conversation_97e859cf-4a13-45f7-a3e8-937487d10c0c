import { getDatabase } from '@/lib/database';
import { v4 as uuidv4 } from 'uuid';

// Project type definition
export interface Project {
  id: string;
  projectTitle: string;
  drivers: string;
  type: string;
  year: number;
  opdFocal: string;
  capex: number;
  opex: number;
  status: string;
  subStatus: string;
  department: string;
  departmentId: string;
  area: string;
  awardedCompany: string;
  savings: number;
  percentage: number;
  startDate: Date | null;
  endDate: Date | null;
  dateOfReceiveFinalDoc: Date | null;
  quarterOfYear: string;
  column1: string;
  createdBy: string;
  statusChangeNote: string;
  statusChangeDate: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

// Helper function to convert database row to Project
function rowToProject(row: any): Project {
  return {
    id: row.id,
    projectTitle: row.projectTitle,
    drivers: row.drivers || '',
    type: row.type || '',
    year: row.year || new Date().getFullYear(),
    opdFocal: row.opdFocal || '',
    capex: row.capex || 0,
    opex: row.opex || 0,
    status: row.status || 'Planning',
    subStatus: row.subStatus || '',
    department: row.department || '',
    departmentId: row.departmentId || '',
    area: row.area || '',
    awardedCompany: row.awardedCompany || '',
    savings: row.savings || 0,
    percentage: row.percentage || 0,
    startDate: row.startDate ? new Date(row.startDate) : null,
    endDate: row.endDate ? new Date(row.endDate) : null,
    dateOfReceiveFinalDoc: row.dateOfReceiveFinalDoc ? new Date(row.dateOfReceiveFinalDoc) : null,
    quarterOfYear: row.quarterOfYear || 'Q1',
    column1: row.column1 || '',
    createdBy: row.createdBy || '',
    statusChangeNote: row.statusChangeNote || '',
    statusChangeDate: row.statusChangeDate ? new Date(row.statusChangeDate) : null,
    createdAt: new Date(row.createdAt),
    updatedAt: new Date(row.updatedAt)
  };
}

// Get all projects
export async function getAllProjects(): Promise<Project[]> {
  try {
    const db = getDatabase();

    const stmt = db.prepare(`
      SELECT * FROM projects
      ORDER BY createdAt DESC
    `);

    const rows = stmt.all() as any[];
    return rows.map(rowToProject);
  } catch (error) {
    console.error('Error getting projects:', error);
    throw error;
  }
}

// Get project by ID
export async function getProjectById(id: string): Promise<Project | null> {
  try {
    const db = getDatabase();

    const stmt = db.prepare('SELECT * FROM projects WHERE id = ?');
    const row = stmt.get(id) as any;

    if (!row) {
      return null;
    }

    return rowToProject(row);
  } catch (error) {
    console.error(`Error getting project ${id}:`, error);
    throw error;
  }
}

// Create new project
export async function createProject(projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>): Promise<Project> {
  try {
    const db = getDatabase();
    const id = uuidv4();
    const now = new Date().toISOString();

    const stmt = db.prepare(`
      INSERT INTO projects (
        id, projectTitle, drivers, type, year, opdFocal, capex, opex, status, subStatus,
        department, departmentId, area, awardedCompany, savings, percentage,
        startDate, endDate, dateOfReceiveFinalDoc, quarterOfYear, column1,
        createdBy, statusChangeNote, statusChangeDate, createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      id,
      projectData.projectTitle,
      projectData.drivers,
      projectData.type,
      projectData.year,
      projectData.opdFocal,
      projectData.capex,
      projectData.opex,
      projectData.status,
      projectData.subStatus,
      projectData.department,
      projectData.departmentId,
      projectData.area,
      projectData.awardedCompany,
      projectData.savings,
      projectData.percentage,
      projectData.startDate?.toISOString(),
      projectData.endDate?.toISOString(),
      projectData.dateOfReceiveFinalDoc?.toISOString(),
      projectData.quarterOfYear,
      projectData.column1,
      projectData.createdBy,
      projectData.statusChangeNote,
      projectData.statusChangeDate?.toISOString(),
      now,
      now
    );

    const newProject = await getProjectById(id);
    if (!newProject) {
      throw new Error('Failed to create project');
    }

    return newProject;
  } catch (error) {
    console.error('Error creating project:', error);
    throw error;
  }
}

// Update project
export async function updateProject(id: string, projectData: Partial<Project>): Promise<Project> {
  try {
    const db = getDatabase();
    const now = new Date().toISOString();

    // Build dynamic update query
    const fields = [];
    const values = [];

    // Add all possible fields
    const fieldMappings = [
      'projectTitle', 'drivers', 'type', 'year', 'opdFocal', 'capex', 'opex',
      'status', 'subStatus', 'department', 'departmentId', 'area', 'awardedCompany',
      'savings', 'percentage', 'quarterOfYear', 'column1', 'createdBy',
      'statusChangeNote'
    ];

    fieldMappings.forEach(field => {
      if (projectData[field as keyof Project] !== undefined) {
        fields.push(`${field} = ?`);
        values.push(projectData[field as keyof Project]);
      }
    });

    // Handle date fields
    if (projectData.startDate !== undefined) {
      fields.push('startDate = ?');
      values.push(projectData.startDate?.toISOString() || null);
    }
    if (projectData.endDate !== undefined) {
      fields.push('endDate = ?');
      values.push(projectData.endDate?.toISOString() || null);
    }
    if (projectData.dateOfReceiveFinalDoc !== undefined) {
      fields.push('dateOfReceiveFinalDoc = ?');
      values.push(projectData.dateOfReceiveFinalDoc?.toISOString() || null);
    }
    if (projectData.statusChangeDate !== undefined) {
      fields.push('statusChangeDate = ?');
      values.push(projectData.statusChangeDate?.toISOString() || null);
    }

    fields.push('updatedAt = ?');
    values.push(now);
    values.push(id);

    const stmt = db.prepare(`
      UPDATE projects
      SET ${fields.join(', ')}
      WHERE id = ?
    `);

    stmt.run(...values);

    const updatedProject = await getProjectById(id);
    if (!updatedProject) {
      throw new Error(`Project with ID ${id} not found`);
    }

    return updatedProject;
  } catch (error) {
    console.error(`Error updating project ${id}:`, error);
    throw error;
  }
}

// Delete project
export async function deleteProject(id: string): Promise<void> {
  try {
    const db = getDatabase();

    const stmt = db.prepare('DELETE FROM projects WHERE id = ?');
    const result = stmt.run(id);

    if (result.changes === 0) {
      throw new Error(`Project with ID ${id} not found`);
    }
  } catch (error) {
    console.error(`Error deleting project ${id}:`, error);
    throw error;
  }
}

// Search projects
export async function searchProjects(searchTerm: string): Promise<Project[]> {
  try {
    const db = getDatabase();
    const lowerSearchTerm = `%${searchTerm.toLowerCase()}%`;

    const stmt = db.prepare(`
      SELECT * FROM projects
      WHERE
        LOWER(projectTitle) LIKE ? OR
        LOWER(department) LIKE ? OR
        LOWER(opdFocal) LIKE ? OR
        LOWER(area) LIKE ? OR
        LOWER(status) LIKE ?
      ORDER BY createdAt DESC
    `);

    const rows = stmt.all(lowerSearchTerm, lowerSearchTerm, lowerSearchTerm, lowerSearchTerm, lowerSearchTerm) as any[];
    return rows.map(rowToProject);
  } catch (error) {
    console.error('Error searching projects:', error);
    throw error;
  }
}

// Get projects by department
export async function getProjectsByDepartment(departmentId: string): Promise<Project[]> {
  try {
    const db = getDatabase();

    const stmt = db.prepare(`
      SELECT * FROM projects
      WHERE departmentId = ?
      ORDER BY createdAt DESC
    `);

    const rows = stmt.all(departmentId) as any[];
    return rows.map(rowToProject);
  } catch (error) {
    console.error(`Error getting projects by department ${departmentId}:`, error);
    throw error;
  }
}

// Get projects by status
export async function getProjectsByStatus(status: string): Promise<Project[]> {
  try {
    const db = getDatabase();

    const stmt = db.prepare(`
      SELECT * FROM projects
      WHERE status = ?
      ORDER BY createdAt DESC
    `);

    const rows = stmt.all(status) as any[];
    return rows.map(rowToProject);
  } catch (error) {
    console.error(`Error getting projects by status ${status}:`, error);
    throw error;
  }
}

// Get project statistics
export async function getProjectStats(): Promise<{
  total: number;
  byStatus: Record<string, number>;
  byDepartment: Record<string, number>;
  totalBudget: number;
  totalSavings: number;
}> {
  try {
    const db = getDatabase();

    // Get total count
    const totalStmt = db.prepare('SELECT COUNT(*) as count FROM projects');
    const totalResult = totalStmt.get() as any;

    // Get by status
    const statusStmt = db.prepare(`
      SELECT status, COUNT(*) as count
      FROM projects
      GROUP BY status
    `);
    const statusResults = statusStmt.all() as any[];
    const byStatus: Record<string, number> = {};
    statusResults.forEach(row => {
      byStatus[row.status] = row.count;
    });

    // Get by department
    const deptStmt = db.prepare(`
      SELECT department, COUNT(*) as count
      FROM projects
      GROUP BY department
    `);
    const deptResults = deptStmt.all() as any[];
    const byDepartment: Record<string, number> = {};
    deptResults.forEach(row => {
      byDepartment[row.department] = row.count;
    });

    // Get budget and savings totals
    const budgetStmt = db.prepare(`
      SELECT
        COALESCE(SUM(capex + opex), 0) as totalBudget,
        COALESCE(SUM(savings), 0) as totalSavings
      FROM projects
    `);
    const budgetResult = budgetStmt.get() as any;

    return {
      total: totalResult.count,
      byStatus,
      byDepartment,
      totalBudget: budgetResult.totalBudget || 0,
      totalSavings: budgetResult.totalSavings || 0
    };
  } catch (error) {
    console.error('Error getting project stats:', error);
    throw error;
  }
}

// Get projects by user (opdFocal)
export async function getProjectsByUser(userName: string): Promise<Project[]> {
  try {
    const db = getDatabase();

    const stmt = db.prepare(`
      SELECT * FROM projects
      WHERE opdFocal = ?
      ORDER BY createdAt DESC
    `);

    const rows = stmt.all(userName) as any[];
    return rows.map(rowToProject);
  } catch (error) {
    console.error(`Error getting projects by user ${userName}:`, error);
    throw error;
  }
}
