import { NextRequest, NextResponse } from 'next/server';
import {
  getDepartmentById,
  updateDepartment,
  deleteDepartment,
  departmentNameExists
} from '@/services/sqlite/departmentService';
import { getUsersByDepartment } from '@/services/sqlite/userService';
import { getProjectsByDepartment } from '@/services/sqlite/projectService';

// GET /api/departments/[id] - Get department by ID
export async function GET(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const { id } = context.params;

    const department = await getDepartmentById(id);

    if (!department) {
      return NextResponse.json(
        { error: 'Department not found' },
        { status: 404 }
      );
    }

    // Get users in department
    const users = await getUsersByDepartment(id);
    const safeUsers = users.map(user => {
      const { password, ...userWithoutPassword } = user;
      return {
        id: userWithoutPassword.id,
        name: userWithoutPassword.name,
        email: userWithoutPassword.email,
        role: userWithoutPassword.role
      };
    });

    // Get projects in department
    const projects = await getProjectsByDepartment(id);
    const projectSummaries = projects.map(project => ({
      id: project.id,
      projectTitle: project.projectTitle,
      status: project.status,
      budget: project.capex + project.opex,
      percentage: project.percentage
    }));

    // Return department with related data
    return NextResponse.json({
      ...department,
      users: safeUsers,
      projects: projectSummaries
    });
  } catch (error) {
    console.error('Error fetching department:', error);
    return NextResponse.json(
      { error: 'Failed to fetch department' },
      { status: 500 }
    );
  }
}

// PATCH /api/departments/[id] - Update department by ID
export async function PATCH(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const { id } = context.params;
    const body = await request.json();

    // Validate required fields
    if (body.name !== undefined && body.name.trim() === '') {
      return NextResponse.json(
        { error: 'Department name cannot be empty' },
        { status: 400 }
      );
    }

    // Check if department exists
    const existingDepartment = await getDepartmentById(id);
    if (!existingDepartment) {
      return NextResponse.json(
        { error: 'Department not found' },
        { status: 404 }
      );
    }

    // Check if another department with the same name exists
    if (body.name && body.name !== existingDepartment.name) {
      const exists = await departmentNameExists(body.name, id);
      if (exists) {
        return NextResponse.json(
          { error: 'Another department with this name already exists' },
          { status: 400 }
        );
      }
    }

    // Update the department
    const updateData: any = {};
    if (body.name !== undefined) updateData.name = body.name;
    if (body.description !== undefined) updateData.description = body.description;
    if (body.budget !== undefined) updateData.budget = parseFloat(body.budget);

    const department = await updateDepartment(id, updateData);

    return NextResponse.json(department);
  } catch (error) {
    console.error('Error updating department:', error);
    return NextResponse.json(
      { error: 'Failed to update department' },
      { status: 500 }
    );
  }
}

// DELETE /api/departments/[id] - Delete department by ID
export async function DELETE(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const { id } = context.params;

    await deleteDepartment(id);

    return new NextResponse(null, { status: 204 });
  } catch (error: any) {
    console.error('Error deleting department:', error);

    // Check for specific error messages
    if (error.message?.includes('associated users') || error.message?.includes('associated projects')) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to delete department' },
      { status: 500 }
    );
  }
}