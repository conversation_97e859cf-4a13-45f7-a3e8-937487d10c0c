// Script to set up local development environment
const path = require('path');
const fs = require('fs');

// Create local project platform directory
const LOCAL_PATH = path.join(process.cwd(), 'local-data');

console.log('🚀 Setting up local development environment...\n');

try {
  // Create local data directory
  if (!fs.existsSync(LOCAL_PATH)) {
    fs.mkdirSync(LOCAL_PATH, { recursive: true });
    console.log(`✅ Created local data directory: ${LOCAL_PATH}`);
  } else {
    console.log(`✅ Local data directory already exists: ${LOCAL_PATH}`);
  }

  // Create uploads subdirectory
  const uploadsPath = path.join(LOCAL_PATH, 'uploads');
  if (!fs.existsSync(uploadsPath)) {
    fs.mkdirSync(uploadsPath, { recursive: true });
    console.log(`✅ Created uploads directory: ${uploadsPath}`);
  } else {
    console.log(`✅ Uploads directory already exists: ${uploadsPath}`);
  }

  // Create .env.local file with local configuration
  const envPath = path.join(process.cwd(), '.env.local');
  const envContent = `# Local Development Configuration
SHARED_PATH=${LOCAL_PATH.replace(/\\/g, '/')}
NEXTAUTH_SECRET=local-development-secret-key
NEXTAUTH_URL=http://localhost:3000
NODE_ENV=development
`;

  fs.writeFileSync(envPath, envContent);
  console.log(`✅ Created .env.local with local configuration`);

  console.log('\n📋 Configuration:');
  console.log(`   Local Path: ${LOCAL_PATH}`);
  console.log(`   Database: ${path.join(LOCAL_PATH, 'data.sqlite')}`);
  console.log(`   Uploads: ${uploadsPath}`);

  console.log('\n🎉 Local environment setup completed!');
  console.log('\n📝 Next steps:');
  console.log('   1. Run "npm run init:db" to initialize the database');
  console.log('   2. Run "npm run seed:sqlite" to populate with sample data');
  console.log('   3. Run "npm run dev" to start the development server');

} catch (error) {
  console.error('\n❌ Setup failed:', error.message);
  process.exit(1);
}
