import { getDatabase, PATHS } from '@/lib/database';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';

// Project file type definition
export interface ProjectFile {
  id: string;
  projectId: string;
  filename: string;
  originalName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  uploadedBy: string | null;
  uploadedAt: Date;
}

// Helper function to convert database row to ProjectFile
function rowToProjectFile(row: any): ProjectFile {
  return {
    id: row.id,
    projectId: row.projectId,
    filename: row.filename,
    originalName: row.originalName,
    filePath: row.filePath,
    fileSize: row.fileSize,
    mimeType: row.mimeType,
    uploadedBy: row.uploadedBy,
    uploadedAt: new Date(row.uploadedAt)
  };
}

// Get all files for a project
export async function getProjectFiles(projectId: string): Promise<ProjectFile[]> {
  try {
    const db = getDatabase();
    
    const stmt = db.prepare(`
      SELECT * FROM project_files 
      WHERE projectId = ?
      ORDER BY uploadedAt DESC
    `);
    
    const rows = stmt.all(projectId) as any[];
    return rows.map(rowToProjectFile);
  } catch (error) {
    console.error(`Error getting project files for ${projectId}:`, error);
    throw error;
  }
}

// Get file by ID
export async function getProjectFileById(id: string): Promise<ProjectFile | null> {
  try {
    const db = getDatabase();
    
    const stmt = db.prepare('SELECT * FROM project_files WHERE id = ?');
    const row = stmt.get(id) as any;
    
    if (!row) {
      return null;
    }
    
    return rowToProjectFile(row);
  } catch (error) {
    console.error(`Error getting project file ${id}:`, error);
    throw error;
  }
}

// Save file to disk and database
export async function saveProjectFile(
  projectId: string,
  file: {
    buffer: Buffer;
    originalname: string;
    mimetype: string;
    size: number;
  },
  uploadedBy?: string
): Promise<ProjectFile> {
  try {
    const db = getDatabase();
    const id = uuidv4();
    const now = new Date().toISOString();
    
    // Generate unique filename
    const ext = path.extname(file.originalname);
    const filename = `${id}${ext}`;
    const filePath = path.join(PATHS.UPLOADS, filename);
    
    // Ensure uploads directory exists
    if (!fs.existsSync(PATHS.UPLOADS)) {
      fs.mkdirSync(PATHS.UPLOADS, { recursive: true });
    }
    
    // Write file to disk
    fs.writeFileSync(filePath, file.buffer);
    
    // Save file metadata to database
    const stmt = db.prepare(`
      INSERT INTO project_files (
        id, projectId, filename, originalName, filePath, fileSize, mimeType, uploadedBy, uploadedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    stmt.run(
      id,
      projectId,
      filename,
      file.originalname,
      filePath,
      file.size,
      file.mimetype,
      uploadedBy,
      now
    );
    
    const newFile = await getProjectFileById(id);
    if (!newFile) {
      throw new Error('Failed to save project file');
    }
    
    return newFile;
  } catch (error) {
    console.error('Error saving project file:', error);
    throw error;
  }
}

// Delete file from disk and database
export async function deleteProjectFile(id: string): Promise<void> {
  try {
    const db = getDatabase();
    
    // Get file info first
    const file = await getProjectFileById(id);
    if (!file) {
      throw new Error(`Project file with ID ${id} not found`);
    }
    
    // Delete from database
    const stmt = db.prepare('DELETE FROM project_files WHERE id = ?');
    const result = stmt.run(id);
    
    if (result.changes === 0) {
      throw new Error(`Project file with ID ${id} not found`);
    }
    
    // Delete file from disk
    try {
      if (fs.existsSync(file.filePath)) {
        fs.unlinkSync(file.filePath);
      }
    } catch (fileError) {
      console.warn(`Could not delete file from disk: ${file.filePath}`, fileError);
      // Don't throw error here as the database record is already deleted
    }
  } catch (error) {
    console.error(`Error deleting project file ${id}:`, error);
    throw error;
  }
}

// Delete all files for a project
export async function deleteAllProjectFiles(projectId: string): Promise<void> {
  try {
    const files = await getProjectFiles(projectId);
    
    // Delete each file
    for (const file of files) {
      await deleteProjectFile(file.id);
    }
  } catch (error) {
    console.error(`Error deleting all project files for ${projectId}:`, error);
    throw error;
  }
}

// Get file content for download
export async function getFileContent(id: string): Promise<{ file: ProjectFile; content: Buffer }> {
  try {
    const file = await getProjectFileById(id);
    if (!file) {
      throw new Error(`Project file with ID ${id} not found`);
    }
    
    if (!fs.existsSync(file.filePath)) {
      throw new Error(`File not found on disk: ${file.filePath}`);
    }
    
    const content = fs.readFileSync(file.filePath);
    
    return { file, content };
  } catch (error) {
    console.error(`Error getting file content for ${id}:`, error);
    throw error;
  }
}

// Check if file exists on disk
export async function checkFileExists(id: string): Promise<boolean> {
  try {
    const file = await getProjectFileById(id);
    if (!file) {
      return false;
    }
    
    return fs.existsSync(file.filePath);
  } catch (error) {
    console.error(`Error checking file existence for ${id}:`, error);
    return false;
  }
}

// Get file statistics
export async function getFileStats(): Promise<{
  totalFiles: number;
  totalSize: number;
  filesByProject: Record<string, number>;
  filesByType: Record<string, number>;
}> {
  try {
    const db = getDatabase();
    
    // Get total count and size
    const totalStmt = db.prepare(`
      SELECT 
        COUNT(*) as totalFiles,
        COALESCE(SUM(fileSize), 0) as totalSize
      FROM project_files
    `);
    const totalResult = totalStmt.get() as any;
    
    // Get files by project
    const projectStmt = db.prepare(`
      SELECT projectId, COUNT(*) as count 
      FROM project_files 
      GROUP BY projectId
    `);
    const projectResults = projectStmt.all() as any[];
    const filesByProject: Record<string, number> = {};
    projectResults.forEach(row => {
      filesByProject[row.projectId] = row.count;
    });
    
    // Get files by type
    const typeStmt = db.prepare(`
      SELECT mimeType, COUNT(*) as count 
      FROM project_files 
      GROUP BY mimeType
    `);
    const typeResults = typeStmt.all() as any[];
    const filesByType: Record<string, number> = {};
    typeResults.forEach(row => {
      filesByType[row.mimeType] = row.count;
    });
    
    return {
      totalFiles: totalResult.totalFiles || 0,
      totalSize: totalResult.totalSize || 0,
      filesByProject,
      filesByType
    };
  } catch (error) {
    console.error('Error getting file stats:', error);
    throw error;
  }
}

// Validate file type and size
export function validateFile(file: { mimetype: string; size: number }): { valid: boolean; error?: string } {
  const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
  const ALLOWED_TYPES = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'text/csv',
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/zip',
    'application/x-zip-compressed'
  ];
  
  if (file.size > MAX_FILE_SIZE) {
    return { valid: false, error: 'File size exceeds 50MB limit' };
  }
  
  if (!ALLOWED_TYPES.includes(file.mimetype)) {
    return { valid: false, error: 'File type not allowed' };
  }
  
  return { valid: true };
}
