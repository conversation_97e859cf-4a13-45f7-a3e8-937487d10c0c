// Script to migrate data from Firebase to SQLite
const { initializeApp } = require('firebase/app');
const { getFirestore, collection, getDocs } = require('firebase/firestore');
const Database = require('better-sqlite3');
const { v4: uuidv4 } = require('uuid');
const path = require('path');
const fs = require('fs');

// Load environment variables from .env.local
function loadEnvFile() {
  const envPath = path.join(process.cwd(), '.env.local');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');

    lines.forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          const value = valueParts.join('=');
          process.env[key.trim()] = value.trim();
        }
      }
    });

    console.log('✅ Loaded environment variables from .env.local');
  }
}

// Load environment variables
loadEnvFile();

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAkGE-lbEzcRVJZbKjE_SHJd38jENqut8k",
  authDomain: "project-management-f45cc.firebaseapp.com",
  projectId: "project-management-f45cc",
  storageBucket: "project-management-f45cc.firebasestorage.app",
  messagingSenderId: "1002222709659",
  appId: "1:1002222709659:web:6b1ab479efcc4102824f3e"
};

// Initialize Firebase
const firebaseApp = initializeApp(firebaseConfig);
const firebaseDb = getFirestore(firebaseApp);

// SQLite configuration
const CONFIG = {
  SHARED_PATH: process.env.SHARED_PATH || 'Z:/ProjectPlatform',
  DATABASE_NAME: 'data.sqlite',
  UPLOADS_FOLDER: 'uploads'
};

const PATHS = {
  DATABASE: path.join(CONFIG.SHARED_PATH, CONFIG.DATABASE_NAME),
  UPLOADS: path.join(CONFIG.SHARED_PATH, CONFIG.UPLOADS_FOLDER)
};

let sqliteDb = null;

function getSQLiteDatabase() {
  if (!sqliteDb) {
    sqliteDb = new Database(PATHS.DATABASE);
    sqliteDb.pragma('foreign_keys = ON');
  }
  return sqliteDb;
}

// Helper function to convert Firebase timestamp to ISO string
function convertTimestamp(timestamp) {
  if (!timestamp) return new Date().toISOString();
  if (timestamp.toDate) return timestamp.toDate().toISOString();
  if (timestamp.seconds) return new Date(timestamp.seconds * 1000).toISOString();
  return new Date(timestamp).toISOString();
}

// Migrate departments from Firebase to SQLite
async function migrateDepartments() {
  console.log('\n🏢 Migrating departments from Firebase...');

  try {
    // Get departments from Firebase
    const departmentsSnapshot = await getDocs(collection(firebaseDb, 'departments'));
    const firebaseDepartments = [];

    departmentsSnapshot.forEach((doc) => {
      firebaseDepartments.push({
        firebaseId: doc.id,
        ...doc.data()
      });
    });

    console.log(`Found ${firebaseDepartments.length} departments in Firebase`);

    if (firebaseDepartments.length === 0) {
      console.log('⚠️  No departments found in Firebase');
      return {};
    }

    // Clear existing departments in SQLite
    const db = getSQLiteDatabase();
    db.prepare('DELETE FROM departments').run();
    console.log('🗑️  Cleared existing departments in SQLite');

    // Insert departments into SQLite
    const departmentMapping = {};
    const insertStmt = db.prepare(`
      INSERT INTO departments (id, name, description, budget, createdAt, updatedAt)
      VALUES (?, ?, ?, ?, ?, ?)
    `);

    for (const dept of firebaseDepartments) {
      const newId = uuidv4();
      const now = new Date().toISOString();

      insertStmt.run(
        newId,
        dept.name || '',
        dept.description || '',
        dept.budget || 0,
        convertTimestamp(dept.createdAt) || now,
        convertTimestamp(dept.updatedAt) || now
      );

      departmentMapping[dept.firebaseId] = {
        id: newId,
        name: dept.name
      };

      console.log(`✅ Migrated department: ${dept.name}`);
    }

    console.log(`🎉 Successfully migrated ${firebaseDepartments.length} departments`);
    return departmentMapping;

  } catch (error) {
    console.error('❌ Error migrating departments:', error);
    throw error;
  }
}

// Migrate users from Firebase to SQLite
async function migrateUsers(departmentMapping) {
  console.log('\n👥 Migrating users from Firebase...');

  try {
    // Get users from Firebase
    const usersSnapshot = await getDocs(collection(firebaseDb, 'users'));
    const firebaseUsers = [];

    usersSnapshot.forEach((doc) => {
      firebaseUsers.push({
        firebaseId: doc.id,
        ...doc.data()
      });
    });

    console.log(`Found ${firebaseUsers.length} users in Firebase`);

    if (firebaseUsers.length === 0) {
      console.log('⚠️  No users found in Firebase');
      return {};
    }

    // Clear existing users in SQLite
    const db = getSQLiteDatabase();
    db.prepare('DELETE FROM users').run();
    console.log('🗑️  Cleared existing users in SQLite');

    // Insert users into SQLite
    const userMapping = {};
    const insertStmt = db.prepare(`
      INSERT INTO users (
        id, name, email, password, role, department, departmentId,
        phone, bio, jobTitle, createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    for (const user of firebaseUsers) {
      const newId = uuidv4();
      const now = new Date().toISOString();

      // Map department
      let departmentId = null;
      let departmentName = user.department || '';

      if (user.departmentId && departmentMapping[user.departmentId]) {
        departmentId = departmentMapping[user.departmentId].id;
        departmentName = departmentMapping[user.departmentId].name;
      } else if (user.departmentName) {
        // Try to find department by name
        const deptEntry = Object.values(departmentMapping).find(d => d.name === user.departmentName);
        if (deptEntry) {
          departmentId = deptEntry.id;
          departmentName = deptEntry.name;
        }
      }

      // Handle duplicate emails by adding a suffix
      let email = user.email || '';
      let emailSuffix = 0;
      let originalEmail = email;

      while (true) {
        try {
          insertStmt.run(
            newId,
            user.name || '',
            email,
            user.password || '',
            user.role || 'USER',
            departmentName,
            departmentId,
            user.phone || null,
            user.bio || null,
            user.jobTitle || null,
            convertTimestamp(user.createdAt) || now,
            convertTimestamp(user.updatedAt) || now
          );
          break; // Success, exit the loop
        } catch (error) {
          if (error.code === 'SQLITE_CONSTRAINT_UNIQUE' && error.message.includes('users.email')) {
            emailSuffix++;
            const emailParts = originalEmail.split('@');
            if (emailParts.length === 2) {
              email = `${emailParts[0]}.${emailSuffix}@${emailParts[1]}`;
            } else {
              email = `${originalEmail}.${emailSuffix}`;
            }
            console.log(`⚠️  Email conflict, trying: ${email}`);
          } else {
            throw error; // Re-throw if it's a different error
          }
        }
      }

      userMapping[user.firebaseId] = {
        id: newId,
        name: user.name,
        email: email
      };

      if (email !== originalEmail) {
        console.log(`✅ Migrated user: ${user.name} (${originalEmail} → ${email})`);
      } else {
        console.log(`✅ Migrated user: ${user.name} (${email})`);
      }
    }

    console.log(`🎉 Successfully migrated ${firebaseUsers.length} users`);
    return userMapping;

  } catch (error) {
    console.error('❌ Error migrating users:', error);
    throw error;
  }
}

// Migrate projects from Firebase to SQLite
async function migrateProjects(departmentMapping, userMapping) {
  console.log('\n📋 Migrating projects from Firebase...');

  try {
    // Get projects from Firebase
    const projectsSnapshot = await getDocs(collection(firebaseDb, 'projects'));
    const firebaseProjects = [];

    projectsSnapshot.forEach((doc) => {
      firebaseProjects.push({
        firebaseId: doc.id,
        ...doc.data()
      });
    });

    console.log(`Found ${firebaseProjects.length} projects in Firebase`);

    if (firebaseProjects.length === 0) {
      console.log('⚠️  No projects found in Firebase');
      return {};
    }

    // Clear existing projects in SQLite
    const db = getSQLiteDatabase();
    db.prepare('DELETE FROM projects').run();
    console.log('🗑️  Cleared existing projects in SQLite');

    // Insert projects into SQLite
    const projectMapping = {};
    const insertStmt = db.prepare(`
      INSERT INTO projects (
        id, projectTitle, drivers, type, year, opdFocal, capex, opex, status, subStatus,
        department, departmentId, area, awardedCompany, savings, percentage,
        startDate, endDate, dateOfReceiveFinalDoc, quarterOfYear, column1,
        createdBy, statusChangeNote, statusChangeDate, createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    for (const project of firebaseProjects) {
      const newId = uuidv4();
      const now = new Date().toISOString();

      // Map department
      let departmentId = null;
      let departmentName = project.department || '';

      if (project.departmentId && departmentMapping[project.departmentId]) {
        departmentId = departmentMapping[project.departmentId].id;
        departmentName = departmentMapping[project.departmentId].name;
      } else if (project.department) {
        // Try to find department by name
        const deptEntry = Object.values(departmentMapping).find(d => d.name === project.department);
        if (deptEntry) {
          departmentId = deptEntry.id;
          departmentName = deptEntry.name;
        }
      }

      // Map created by user
      let createdBy = project.createdBy || 'system';
      if (project.createdBy && userMapping[project.createdBy]) {
        createdBy = userMapping[project.createdBy].id;
      }

      insertStmt.run(
        newId,
        project.projectTitle || '',
        project.drivers || '',
        project.type || '',
        project.year || new Date().getFullYear(),
        project.opdFocal || '',
        project.capex || 0,
        project.opex || 0,
        project.status || 'Planning',
        project.subStatus || '',
        departmentName,
        departmentId,
        project.area || '',
        project.awardedCompany || '',
        project.savings || 0,
        project.percentage || 0,
        project.startDate ? convertTimestamp(project.startDate) : null,
        project.endDate ? convertTimestamp(project.endDate) : null,
        project.dateOfReceiveFinalDoc ? convertTimestamp(project.dateOfReceiveFinalDoc) : null,
        project.quarterOfYear || 'Q1',
        project.column1 || '',
        createdBy,
        project.statusChangeNote || '',
        project.statusChangeDate ? convertTimestamp(project.statusChangeDate) : null,
        convertTimestamp(project.createdAt) || now,
        convertTimestamp(project.updatedAt) || now
      );

      projectMapping[project.firebaseId] = {
        id: newId,
        title: project.projectTitle
      };

      console.log(`✅ Migrated project: ${project.projectTitle}`);
    }

    console.log(`🎉 Successfully migrated ${firebaseProjects.length} projects`);
    return projectMapping;

  } catch (error) {
    console.error('❌ Error migrating projects:', error);
    throw error;
  }
}

// Create project logs for migrated projects
async function createProjectLogs(projectMapping, userMapping) {
  console.log('\n📝 Creating project logs for migrated projects...');

  try {
    const db = getSQLiteDatabase();

    // Clear existing logs
    db.prepare('DELETE FROM project_logs').run();
    console.log('🗑️  Cleared existing project logs in SQLite');

    const insertStmt = db.prepare(`
      INSERT INTO project_logs (
        id, projectId, action, field, oldValue, newValue, note, userId, userName, createdAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    let logCount = 0;
    for (const [firebaseId, project] of Object.entries(projectMapping)) {
      const logId = uuidv4();
      const now = new Date().toISOString();

      // Find a user to attribute the creation to (preferably admin)
      const adminUser = Object.values(userMapping).find(u => u.email && u.email.includes('admin'));
      const anyUser = Object.values(userMapping)[0];
      const user = adminUser || anyUser;

      insertStmt.run(
        logId,
        project.id,
        'PROJECT_CREATED',
        null,
        null,
        project.title,
        `Project "${project.title}" was migrated from Firebase`,
        user?.id || 'system',
        user?.name || 'System',
        now
      );

      logCount++;
    }

    console.log(`✅ Created ${logCount} project creation logs`);

  } catch (error) {
    console.error('❌ Error creating project logs:', error);
    throw error;
  }
}

// Main migration function
async function migrateFromFirebase() {
  console.log('🚀 Starting Firebase to SQLite migration...\n');

  try {
    // Check if SQLite database exists
    if (!fs.existsSync(PATHS.DATABASE)) {
      console.log('❌ SQLite database not found. Please run "npm run init:db" first.');
      process.exit(1);
    }

    console.log('✅ SQLite database found');

    // Step 1: Migrate departments
    const departmentMapping = await migrateDepartments();

    // Step 2: Migrate users
    const userMapping = await migrateUsers(departmentMapping);

    // Step 3: Migrate projects
    const projectMapping = await migrateProjects(departmentMapping, userMapping);

    // Step 4: Create project logs
    await createProjectLogs(projectMapping, userMapping);

    console.log('\n🎉 Migration completed successfully!');
    console.log('\n📊 Migration Summary:');
    console.log(`   Departments: ${Object.keys(departmentMapping).length}`);
    console.log(`   Users: ${Object.keys(userMapping).length}`);
    console.log(`   Projects: ${Object.keys(projectMapping).length}`);

    console.log('\n🚀 Ready to use the application with migrated data!');
    console.log('   Run "npm run dev" to start the development server');

  } catch (error) {
    console.error('\n❌ Migration failed:', error);
    process.exit(1);
  } finally {
    if (sqliteDb) {
      sqliteDb.close();
    }
  }
}

// Run migration
migrateFromFirebase();
