'use client';

import { useState, useEffect } from 'react';
import AppLayout from '@/components/AppLayout';
import { ArrowTrendingUpIcon, ArrowTrendingDownIcon, ChartBarIcon, CurrencyDollarIcon, ClockIcon, UserGroupIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Doughnut, Bar, Line } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

type ActivityType = 'success' | 'info' | 'warning' | 'danger';

interface Project {
  id: string;
  projectTitle: string;
  status: string;
  percentage: number;
  department: string;
  opdFocal: string;
  budget?: number;
  awardAmount?: number;
  savingsOMR?: number;
  completionDate?: string;
  startDate?: string;
  createdAt: string;
  type: string;
}

interface Activity {
  action: string;
  time: string;
  type: ActivityType;
}

export default function AnalyticsPage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchProjects();
  }, []);

  const fetchProjects = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/projects');
      if (!response.ok) {
        throw new Error('Failed to fetch projects');
      }
      const data = await response.json();
      setProjects(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Calculate project status distribution
  const statusDistribution = {
    Possible: projects.filter(p => p.status === 'Possible').length,
    Scoping: projects.filter(p => p.status === 'Scoping').length,
    Procurement: projects.filter(p => p.status === 'Procurement').length,
    Execution: projects.filter(p => p.status === 'Execution').length,
    Completed: projects.filter(p => p.status === 'Completed').length,
    Closed: projects.filter(p => p.status === 'Closed').length,
  };

  // Calculate project completion rate
  const completionStats = {
    onTime: 0,
    delayed: 0,
    atRisk: 0,
    total: projects.length
  };

  projects.forEach(project => {
    const completionDate = project.completionDate ? new Date(project.completionDate) : null;
    const today = new Date();

    if (project.status === 'Completed' || project.status === 'Closed') {
      completionStats.onTime++;
    } else if (completionDate && completionDate < today) {
      completionStats.delayed++;
    } else if (project.percentage < 50 && project.status !== 'Possible' && project.status !== 'Scoping') {
      completionStats.atRisk++;
    }
  });

  // Calculate budget metrics
  const budgetStats = {
    totalBudget: projects.reduce((sum, p) => sum + (p.budget || 0), 0),
    totalAwarded: projects.reduce((sum, p) => sum + (p.awardAmount || 0), 0),
    totalSavings: projects.reduce((sum, p) => sum + (p.savingsOMR || 0), 0)
  };

  // Calculate department distribution
  const departmentData = projects.reduce((acc: {[key: string]: number}, project) => {
    acc[project.department] = (acc[project.department] || 0) + 1;
    return acc;
  }, {});

  // Calculate KPIs
  const activeProjects = projects.filter(p => !['Completed', 'Closed'].includes(p.status)).length;
  const avgDuration = projects
    .filter(p => p.startDate && p.completionDate)
    .map(p => {
      const start = new Date(p.startDate!);
      const end = new Date(p.completionDate!);
      return Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
    })
    .reduce((sum, days, _, arr) => sum + days / arr.length, 0) || 0;

  // Get recent activities
  const recentActivities = projects
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 5)
    .map(p => ({
      action: `Project "${p.projectTitle}" ${p.status === 'Completed' ? 'completed' : 'updated'}`,
      time: new Date(p.createdAt).toLocaleDateString(),
      type: p.status === 'Completed' ? 'success' as ActivityType :
            p.status === 'Delayed' ? 'danger' as ActivityType :
            'info' as ActivityType
    }));

  // Chart data
  const statusChartData = {
    labels: Object.keys(statusDistribution),
    datasets: [
      {
        data: Object.values(statusDistribution),
        backgroundColor: [
          'rgba(148, 163, 184, 0.85)',  // Possible - slate
          'rgba(129, 140, 248, 0.85)',  // Scoping - indigo
          'rgba(96, 165, 250, 0.85)',   // Procurement - blue
          'rgba(251, 191, 36, 0.85)',   // Execution - amber
          'rgba(74, 222, 128, 0.85)',   // Completed - green
          'rgba(248, 113, 113, 0.85)',  // Closed - red
        ],
        borderColor: [
          'rgba(148, 163, 184, 1)',
          'rgba(129, 140, 248, 1)',
          'rgba(96, 165, 250, 1)',
          'rgba(251, 191, 36, 1)',
          'rgba(74, 222, 128, 1)',
          'rgba(248, 113, 113, 1)',
        ],
        borderWidth: 2,
        hoverBorderWidth: 3,
        hoverBorderColor: '#ffffff',
        borderRadius: 4,
      },
    ],
  };

  const budgetChartData = {
    labels: ['Budget', 'Awarded', 'Savings'],
    datasets: [
      {
        label: 'Amount (OMR)',
        data: [
          budgetStats.totalBudget,
          budgetStats.totalAwarded,
          budgetStats.totalSavings
        ],
        backgroundColor: [
          'rgba(96, 165, 250, 0.8)',  // blue
          'rgba(251, 191, 36, 0.8)',  // amber
          'rgba(74, 222, 128, 0.8)',  // green
        ],
        borderColor: [
          'rgba(96, 165, 250, 1)',
          'rgba(251, 191, 36, 1)',
          'rgba(74, 222, 128, 1)',
        ],
        borderWidth: 2,
        borderRadius: 6,
        hoverBorderWidth: 3,
      },
    ],
  };

  // Department distribution chart
  const departmentChartData = {
    labels: Object.keys(departmentData),
    datasets: [
      {
        label: 'Projects by Department',
        data: Object.values(departmentData),
        backgroundColor: 'rgba(129, 140, 248, 0.8)',
        borderColor: 'rgba(129, 140, 248, 1)',
        borderWidth: 2,
        borderRadius: 6,
        hoverBackgroundColor: 'rgba(129, 140, 248, 0.9)',
        hoverBorderColor: 'rgba(129, 140, 248, 1)',
        barThickness: 20,
        maxBarThickness: 30,
      },
    ],
  };

  // Project progress over time (simplified example)
  const progressChartData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Project Completion',
        data: [10, 25, 30, 40, 50, completionStats.onTime],
        fill: true,
        backgroundColor: 'rgba(74, 222, 128, 0.2)',
        borderColor: 'rgba(74, 222, 128, 1)',
        tension: 0.4,
        pointBackgroundColor: 'rgba(74, 222, 128, 1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(74, 222, 128, 1)',
        pointRadius: 4,
        pointHoverRadius: 6,
      },
    ],
  };

  if (loading) {
    return (
      <AppLayout title="Analytics">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout title="Analytics">
        <div className="bg-red-50 p-4 rounded-lg">
          <p className="text-red-600">{error}</p>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout title="">
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent mb-2">
              Analytics
            </h1>
            <p className="text-slate-600 dark:text-slate-400 flex items-center space-x-2">
              <ChartBarIcon className="h-4 w-4" />
              <span>Track project performance and key metrics</span>
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Project Status Distribution */}
          <div className="card p-6 shadow-md hover:shadow-lg transition-shadow dark:bg-slate-800/60 dark:border-slate-700 bg-white border border-slate-200 rounded-xl">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white">Project Status Distribution</h3>
              <div className="flex items-center text-sm font-medium text-green-600 dark:text-green-400">
                <span>{((completionStats.onTime / Math.max(completionStats.total, 1)) * 100).toFixed(1)}%</span>
                <ArrowTrendingUpIcon className="h-4 w-4 ml-1" />
              </div>
            </div>
            <div className="h-64 flex items-center justify-center mb-4">
              <div className="w-full h-full">
                <Doughnut
                  data={statusChartData}
                  options={{
                    maintainAspectRatio: false,
                    cutout: '65%',
                    responsive: true,
                    plugins: {
                      legend: {
                        position: 'bottom',
                        labels: {
                          usePointStyle: true,
                          pointStyle: 'circle',
                          boxWidth: 10,
                          padding: 20,
                          font: {
                            size: 11,
                            weight: 500
                          },
                          color: document.documentElement.classList.contains('dark') ? '#e2e8f0' : '#334155'
                        }
                      },
                      tooltip: {
                        backgroundColor: document.documentElement.classList.contains('dark') ? 'rgba(30, 41, 59, 0.8)' : 'rgba(255, 255, 255, 0.9)',
                        titleColor: document.documentElement.classList.contains('dark') ? '#e2e8f0' : '#334155',
                        bodyColor: document.documentElement.classList.contains('dark') ? '#e2e8f0' : '#334155',
                        borderColor: document.documentElement.classList.contains('dark') ? 'rgba(51, 65, 85, 0.5)' : 'rgba(226, 232, 240, 0.8)',
                        borderWidth: 1,
                        padding: 12,
                        cornerRadius: 6,
                        boxPadding: 4,
                        bodyFont: {
                          size: 12
                        },
                        callbacks: {
                          label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
                            const percentage = Math.round((value as number / total) * 100);
                            return `${label}: ${value} (${percentage}%)`;
                          }
                        }
                      }
                    },
                    animation: {
                      animateScale: true,
                      animateRotate: true,
                      duration: 1000,
                      easing: 'easeOutQuart'
                    }
                  }}
                />
              </div>
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-slate-900 dark:text-white">
                  {((completionStats.onTime / Math.max(completionStats.total, 1)) * 100).toFixed(0)}%
                </p>
                <p className="text-xs text-slate-500 dark:text-slate-400">On-time</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-slate-900 dark:text-white">
                  {((completionStats.delayed / Math.max(completionStats.total, 1)) * 100).toFixed(0)}%
                </p>
                <p className="text-xs text-slate-500 dark:text-slate-400">Delayed</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-slate-900 dark:text-white">
                  {((completionStats.atRisk / Math.max(completionStats.total, 1)) * 100).toFixed(0)}%
                </p>
                <p className="text-xs text-slate-500 dark:text-slate-400">At Risk</p>
              </div>
            </div>
          </div>

          {/* Budget Allocation */}
          <div className="card p-6 shadow-md hover:shadow-lg transition-shadow dark:bg-slate-800/60 dark:border-slate-700 bg-white border border-slate-200 rounded-xl">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white">Budget Allocation</h3>
              <div className="flex items-center text-sm font-medium text-green-600 dark:text-green-400">
                <span>{((budgetStats.totalSavings / Math.max(budgetStats.totalBudget, 1)) * 100).toFixed(1)}% saved</span>
                <ArrowTrendingUpIcon className="h-4 w-4 ml-1" />
              </div>
            </div>
            <div className="h-64 flex items-center justify-center mb-4">
              <div className="w-full h-full">
                <Bar
                  data={budgetChartData}
                  options={{
                    maintainAspectRatio: false,
                    responsive: true,
                    plugins: {
                      legend: {
                        display: false
                      },
                      tooltip: {
                        backgroundColor: document.documentElement.classList.contains('dark') ? 'rgba(30, 41, 59, 0.8)' : 'rgba(255, 255, 255, 0.9)',
                        titleColor: document.documentElement.classList.contains('dark') ? '#e2e8f0' : '#334155',
                        bodyColor: document.documentElement.classList.contains('dark') ? '#e2e8f0' : '#334155',
                        borderColor: document.documentElement.classList.contains('dark') ? 'rgba(51, 65, 85, 0.5)' : 'rgba(226, 232, 240, 0.8)',
                        borderWidth: 1,
                        padding: 12,
                        cornerRadius: 6,
                        boxPadding: 4,
                        callbacks: {
                          label: function(context) {
                            const label = context.dataset.label || '';
                            const value = context.raw || 0;
                            return `${label}: ${value.toLocaleString()} OMR`;
                          }
                        }
                      }
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                        grid: {
                          color: document.documentElement.classList.contains('dark') ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                          lineWidth: 0.5
                        },
                        border: {
                          display: false
                        },
                        ticks: {
                          color: document.documentElement.classList.contains('dark') ? '#e2e8f0' : '#334155',
                          padding: 10,
                          font: {
                            size: 11
                          },
                          callback: function(value) {
                            return (value as number).toLocaleString() + ' OMR';
                          }
                        }
                      },
                      x: {
                        grid: {
                          display: false
                        },
                        border: {
                          display: false
                        },
                        ticks: {
                          color: document.documentElement.classList.contains('dark') ? '#e2e8f0' : '#334155',
                          padding: 8,
                          font: {
                            size: 11
                          }
                        }
                      }
                    },
                    animation: {
                      duration: 1000,
                      easing: 'easeOutQuart'
                    }
                  }}
                />
              </div>
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-slate-900 dark:text-white">{(budgetStats.totalBudget / 1000).toFixed(1)}K</p>
                <p className="text-xs text-slate-500 dark:text-slate-400">Total Budget (OMR)</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-slate-900 dark:text-white">{(budgetStats.totalAwarded / 1000).toFixed(1)}K</p>
                <p className="text-xs text-slate-500 dark:text-slate-400">Awarded (OMR)</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-slate-900 dark:text-white">{(budgetStats.totalSavings / 1000).toFixed(1)}K</p>
                <p className="text-xs text-slate-500 dark:text-slate-400">Savings (OMR)</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Projects by Department */}
          <div className="card p-6 shadow-md hover:shadow-lg transition-shadow dark:bg-slate-800/60 dark:border-slate-700 bg-white border border-slate-200 rounded-xl">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white">Projects by Department</h3>
            </div>
            <div className="h-64 flex items-center justify-center mb-4">
              <div className="w-full h-full">
                <Bar
                  data={departmentChartData}
                  options={{
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    responsive: true,
                    plugins: {
                      legend: {
                        display: false
                      },
                      tooltip: {
                        backgroundColor: document.documentElement.classList.contains('dark') ? 'rgba(30, 41, 59, 0.8)' : 'rgba(255, 255, 255, 0.9)',
                        titleColor: document.documentElement.classList.contains('dark') ? '#e2e8f0' : '#334155',
                        bodyColor: document.documentElement.classList.contains('dark') ? '#e2e8f0' : '#334155',
                        borderColor: document.documentElement.classList.contains('dark') ? 'rgba(51, 65, 85, 0.5)' : 'rgba(226, 232, 240, 0.8)',
                        borderWidth: 1,
                        padding: 12,
                        cornerRadius: 6,
                        boxPadding: 4
                      }
                    },
                    scales: {
                      x: {
                        beginAtZero: true,
                        grid: {
                          color: document.documentElement.classList.contains('dark') ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                          lineWidth: 0.5
                        },
                        border: {
                          display: false
                        },
                        ticks: {
                          color: document.documentElement.classList.contains('dark') ? '#e2e8f0' : '#334155',
                          stepSize: 1,
                          font: {
                            size: 11
                          }
                        }
                      },
                      y: {
                        grid: {
                          display: false
                        },
                        border: {
                          display: false
                        },
                        ticks: {
                          color: document.documentElement.classList.contains('dark') ? '#e2e8f0' : '#334155',
                          font: {
                            size: 11
                          }
                        }
                      }
                    },
                    animation: {
                      duration: 800,
                      easing: 'easeOutQuart'
                    }
                  }}
                />
              </div>
            </div>
          </div>

          {/* Project Progress Over Time */}
          <div className="card p-6 shadow-md hover:shadow-lg transition-shadow dark:bg-slate-800/60 dark:border-slate-700 bg-white border border-slate-200 rounded-xl">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white">Project Completion Trend</h3>
            </div>
            <div className="h-64 flex items-center justify-center mb-4">
              <div className="w-full h-full">
                <Line
                  data={progressChartData}
                  options={{
                    maintainAspectRatio: false,
                    responsive: true,
                    plugins: {
                      legend: {
                        display: false
                      },
                      tooltip: {
                        backgroundColor: document.documentElement.classList.contains('dark') ? 'rgba(30, 41, 59, 0.8)' : 'rgba(255, 255, 255, 0.9)',
                        titleColor: document.documentElement.classList.contains('dark') ? '#e2e8f0' : '#334155',
                        bodyColor: document.documentElement.classList.contains('dark') ? '#e2e8f0' : '#334155',
                        borderColor: document.documentElement.classList.contains('dark') ? 'rgba(51, 65, 85, 0.5)' : 'rgba(226, 232, 240, 0.8)',
                        borderWidth: 1,
                        padding: 12,
                        cornerRadius: 6,
                        boxPadding: 4
                      }
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                        grid: {
                          color: document.documentElement.classList.contains('dark') ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                          lineWidth: 0.5
                        },
                        border: {
                          display: false
                        },
                        ticks: {
                          color: document.documentElement.classList.contains('dark') ? '#e2e8f0' : '#334155',
                          font: {
                            size: 11
                          }
                        }
                      },
                      x: {
                        grid: {
                          display: false
                        },
                        border: {
                          display: false
                        },
                        ticks: {
                          color: document.documentElement.classList.contains('dark') ? '#e2e8f0' : '#334155',
                          font: {
                            size: 11
                          }
                        }
                      }
                    },
                    animation: {
                      duration: 1200,
                      easing: 'easeOutQuart'
                    },
                    elements: {
                      line: {
                        tension: 0.4
                      },
                      point: {
                        radius: 4,
                        hoverRadius: 6
                      }
                    }
                  }}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Key Performance Indicators */}
        <div className="card p-6 shadow-md hover:shadow-lg transition-shadow dark:bg-slate-800/60 dark:border-slate-700 bg-white border border-slate-200 rounded-xl">
          <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-4">Key Performance Indicators</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                label: 'Active Projects',
                value: activeProjects.toString(),
                trend: `${activeProjects - (projects.length - activeProjects)}`,
                positive: true,
                icon: <ChartBarIcon className="h-5 w-5" />
              },
              {
                label: 'Project Success Rate',
                value: `${((completionStats.onTime / Math.max(completionStats.total, 1)) * 100).toFixed(0)}%`,
                trend: '+5%',
                positive: true,
                icon: <UserGroupIcon className="h-5 w-5" />
              },
              {
                label: 'Average Duration',
                value: `${Math.round(avgDuration)} days`,
                trend: '-2 days',
                positive: true,
                icon: <ClockIcon className="h-5 w-5" />
              },
              {
                label: 'Budget Variance',
                value: `${((budgetStats.totalSavings / Math.max(budgetStats.totalBudget, 1)) * 100).toFixed(1)}%`,
                trend: `${((budgetStats.totalSavings / Math.max(budgetStats.totalBudget, 1)) * 100 > 10 ? '+' : '')}${((budgetStats.totalSavings / Math.max(budgetStats.totalBudget, 1)) * 100).toFixed(1)}%`,
                positive: budgetStats.totalSavings > 0,
                icon: <CurrencyDollarIcon className="h-5 w-5" />
              }
            ].map((kpi, index) => (
              <div key={index} className="bg-slate-50 dark:bg-slate-800 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="text-slate-400 dark:text-slate-500">
                    {kpi.icon}
                  </div>
                  <div className={`text-xs font-medium flex items-center ${kpi.positive ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                    <span>{kpi.trend}</span>
                    {kpi.positive ? (
                      <ArrowTrendingUpIcon className="h-3 w-3 ml-1" />
                    ) : (
                      <ArrowTrendingDownIcon className="h-3 w-3 ml-1" />
                    )}
                  </div>
                </div>
                <p className="text-2xl font-bold text-slate-900 dark:text-white mb-1">{kpi.value}</p>
                <p className="text-xs text-slate-500 dark:text-slate-400">{kpi.label}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="card p-6 shadow-md hover:shadow-lg transition-shadow dark:bg-slate-800/60 dark:border-slate-700 bg-white border border-slate-200 rounded-xl">
          <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-4">Recent Activity</h3>
          <div className="divide-y divide-slate-200 dark:divide-slate-700">
            {recentActivities.map((activity, index) => (
              <div key={index} className="py-3 flex items-start">
                <div className={`rounded-full h-8 w-8 flex items-center justify-center flex-shrink-0 mt-1
                  ${activity.type === 'success' ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400' :
                    activity.type === 'danger' ? 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400' :
                    'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400'}`}>
                  {activity.type === 'success' && <CheckCircleIcon className="h-5 w-5" />}
                  {activity.type === 'danger' && <ArrowTrendingDownIcon className="h-5 w-5" />}
                  {activity.type === 'info' && <ArrowTrendingUpIcon className="h-5 w-5" />}
                </div>
                <div className="ml-3 flex-1">
                  <p className="text-sm text-slate-900 dark:text-white">{activity.action}</p>
                  <p className="text-xs text-slate-500 dark:text-slate-400 mt-0.5">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </AppLayout>
  );
}