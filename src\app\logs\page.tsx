'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import AppLayout from '@/components/AppLayout';
import AuthWrapper from '@/components/AuthWrapper';
import {
  ArrowTrendingUpIcon,
  PencilIcon,
  PlusIcon,
  DocumentTextIcon,
  ClipboardDocumentListIcon,
  MagnifyingGlassIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';

interface ProjectLog {
  id: string;
  projectId: string;
  action: 'STATUS_CHANGE' | 'SUBSTATUS_CHANGE' | 'PROJECT_CREATED' | 'PROJECT_UPDATED' | 'NOTE_ADDED';
  description: string;
  changes: Record<string, { from: any; to: any }>;
  note?: string;
  createdBy: string;
  createdByName: string;
  createdAt: string;
}

// Error fallback component
function ErrorFallback({ error, resetErrorBoundary }: { error: Error, resetErrorBoundary: () => void }) {
  return (
    <div className="p-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl text-center">
      <h3 className="text-xl font-semibold text-red-700 dark:text-red-400 mb-2">
        Error loading logs
      </h3>
      <p className="text-sm text-red-600 dark:text-red-300 mb-4">
        {error.message || 'An unexpected error occurred while rendering logs.'}
      </p>
      <button
        onClick={resetErrorBoundary}
        className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
      >
        Try again
      </button>
    </div>
  );
}

export default function ProjectLogsPage() {
  const { data: session } = useSession();
  const [logs, setLogs] = useState<ProjectLog[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<ProjectLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [actionFilter, setActionFilter] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    fetchAllLogs();
  }, []);

  useEffect(() => {
    if (!hasError) {
      filterLogs();
    }
  }, [logs, searchTerm, actionFilter, dateFilter]);

  const fetchAllLogs = async () => {
    try {
      setLoading(true);
      setHasError(false);
      // Use the consolidated logs endpoint
      const response = await fetch('/api/logs');
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to fetch logs: ${errorData.error || response.statusText}`);
      }
      
      const data = await response.json();
      
      // Ensure logs have a valid changes object
      const sanitizedLogs = data.map((log: any) => ({
        ...log,
        changes: log.changes || {}
      }));
      
      // Sort by date descending
      sanitizedLogs.sort((a: ProjectLog, b: ProjectLog) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
      
      setLogs(sanitizedLogs);
      setFilteredLogs(sanitizedLogs);
      console.log(`Loaded ${sanitizedLogs.length} log entries successfully`);
    } catch (error) {
      console.error('Error fetching logs:', error);
      setHasError(true);
      setErrorMessage(error instanceof Error ? error.message : 'Failed to load logs');
    } finally {
      setLoading(false);
    }
  };

  const filterLogs = () => {
    try {
      let filtered = [...logs];

      // Search filter
      if (searchTerm) {
        filtered = filtered.filter(log =>
          log.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          log.createdByName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (log.note && log.note.toLowerCase().includes(searchTerm.toLowerCase()))
        );
      }

      // Action filter
      if (actionFilter) {
        filtered = filtered.filter(log => log.action === actionFilter);
      }

      // Date filter
      if (dateFilter) {
        const filterDate = new Date(dateFilter);
        filtered = filtered.filter(log => {
          const logDate = new Date(log.createdAt);
          return logDate.toDateString() === filterDate.toDateString();
        });
      }

      setFilteredLogs(filtered);
    } catch (error) {
      console.error('Error filtering logs:', error);
      setHasError(true);
      setErrorMessage('Error filtering logs. Please refresh the page.');
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'STATUS_CHANGE':
        return <ArrowTrendingUpIcon className="h-5 w-5 text-blue-500" />;
      case 'SUBSTATUS_CHANGE':
        return <PencilIcon className="h-5 w-5 text-green-500" />;
      case 'PROJECT_CREATED':
        return <PlusIcon className="h-5 w-5 text-purple-500" />;
      case 'PROJECT_UPDATED':
        return <DocumentTextIcon className="h-5 w-5 text-amber-500" />;
      default:
        return <ClipboardDocumentListIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getActionLabel = (action: string) => {
    switch (action) {
      case 'STATUS_CHANGE':
        return 'Status Change';
      case 'SUBSTATUS_CHANGE':
        return 'Sub-Status Change';
      case 'PROJECT_CREATED':
        return 'Project Created';
      case 'PROJECT_UPDATED':
        return 'Project Updated';
      default:
        return 'Other';
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'STATUS_CHANGE':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      case 'SUBSTATUS_CHANGE':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'PROJECT_CREATED':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300';
      case 'PROJECT_UPDATED':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  // Render with proper error handling
  return (
    <AuthWrapper>
      <AppLayout title="Project Activity Logs">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">Project Activity Logs</h1>
              <p className="text-slate-600 dark:text-slate-300 mt-1">
                Track all changes and activities across your projects
              </p>
            </div>
          </div>

          {/* Filters */}
          <div className="card p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Search */}
              <div className="relative">
                <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
                <input
                  type="text"
                  placeholder="Search logs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-slate-200 dark:border-slate-600 dark:bg-slate-700 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Action Filter */}
              <div className="relative">
                <FunnelIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
                <select
                  value={actionFilter}
                  onChange={(e) => setActionFilter(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-slate-200 dark:border-slate-600 dark:bg-slate-700 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none"
                >
                  <option value="">All Actions</option>
                  <option value="STATUS_CHANGE">Status Changes</option>
                  <option value="SUBSTATUS_CHANGE">Sub-Status Changes</option>
                  <option value="PROJECT_CREATED">Project Created</option>
                  <option value="PROJECT_UPDATED">Project Updated</option>
                </select>
              </div>

              {/* Date Filter */}
              <div>
                <input
                  type="date"
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                  className="w-full px-4 py-2 border border-slate-200 dark:border-slate-600 dark:bg-slate-700 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Results count */}
            <div className="mt-4 text-sm text-slate-600 dark:text-slate-300">
              Showing {filteredLogs.length} of {logs.length} log entries
            </div>
          </div>

          {/* Logs */}
          <div className="card">
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
                  <p className="mt-4 text-slate-600 dark:text-slate-300">Loading activity logs...</p>
                </div>
              </div>
            ) : hasError ? (
              <ErrorFallback 
                error={new Error(errorMessage)} 
                resetErrorBoundary={() => {
                  setHasError(false);
                  fetchAllLogs();
                }}
              />
            ) : filteredLogs.length === 0 ? (
              <div className="text-center py-12">
                <ClipboardDocumentListIcon className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                <p className="text-slate-600 dark:text-slate-300">
                  {logs.length === 0 ? 'No activity logs found.' : 'No logs match your current filters.'}
                </p>
              </div>
            ) : (
              <div className="divide-y divide-slate-200 dark:divide-slate-700">
                {filteredLogs.map((log) => (
                  <div key={log.id} className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="flex-shrink-0 mt-1">
                        {getActionIcon(log.action)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-3">
                            <h3 className="font-medium text-slate-900 dark:text-white">
                              {log.description}
                            </h3>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getActionColor(log.action)}`}>
                              {getActionLabel(log.action)}
                            </span>
                          </div>
                          <time className="text-sm text-slate-500 dark:text-slate-400">
                            {new Date(log.createdAt).toLocaleString()}
                          </time>
                        </div>
                        
                        <p className="text-sm text-slate-600 dark:text-slate-300 mb-3">
                          By: <span className="font-medium">{log.createdByName || 'Unknown'}</span>
                        </p>

                        {log.note && (
                          <div className="bg-slate-50 dark:bg-slate-700 rounded-lg p-3 mb-3">
                            <p className="text-sm text-slate-700 dark:text-slate-300">
                              <span className="font-medium">Note:</span> {log.note}
                            </p>
                          </div>
                        )}

                        {log.changes && Object.keys(log.changes).length > 0 && (
                          <div className="space-y-2">
                            <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300">Changes:</h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                              {Object.entries(log.changes || {}).map(([field, change]: [string, any]) => (
                                <div key={field} className="text-sm">
                                  <span className="font-medium text-slate-600 dark:text-slate-400 capitalize">
                                    {field.replace(/([A-Z])/g, ' $1').trim()}:
                                  </span>
                                  <div className="flex items-center gap-2 mt-1">
                                    <span className="bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 px-2 py-1 rounded text-xs">
                                      {change && change.from !== undefined ? String(change.from) : 'Empty'}
                                    </span>
                                    <span className="text-slate-400">→</span>
                                    <span className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-2 py-1 rounded text-xs">
                                      {change && change.to !== undefined ? String(change.to) : 'Empty'}
                                    </span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </AppLayout>
    </AuthWrapper>
  );
} 