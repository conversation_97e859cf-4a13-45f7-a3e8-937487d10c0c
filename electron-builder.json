{"appId": "com.company.project-management-app", "productName": "Project Management App", "directories": {"output": "dist"}, "files": ["out/**/*", "electron/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "src/lib", "to": "app/src/lib"}, {"from": "src/services", "to": "app/src/services"}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "electron/assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Project Management App"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "electron/assets/icon.icns"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}], "icon": "electron/assets/icon.png"}, "publish": null}