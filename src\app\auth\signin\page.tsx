'use client';

import { useState, useEffect } from 'react';
import { signIn } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';

export default function SignIn() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams?.get('callbackUrl') || '/';
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showTroubleshooting, setShowTroubleshooting] = useState(false);
  const isDev = process.env.NODE_ENV !== 'production';

  // Check if we're coming from a cache clear attempt
  useEffect(() => {
    if (searchParams?.get('nocache')) {
      setError('Cache has been cleared. Try logging in again.');
    }
  }, [searchParams]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    const formData = new FormData(e.currentTarget);
    const email = formData.get('email') as string;
    const password = formData.get('password') as string;

    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
        callbackUrl,
      });

      if (result?.error) {
        setError('Invalid email or password');
      } else {
        router.push(callbackUrl);
        router.refresh();
      }
    } catch (error) {
      setError('An error occurred. Please try again.');
      console.error('Sign in error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuickLogin = async () => {
    setIsLoading(true);
    setError('');

    try {
      const result = await signIn('credentials', {
        email: '<EMAIL>',
        password: 'password',
        redirect: false,
        callbackUrl,
      });

      if (result?.error) {
        setError('Development login failed. Try regular login instead.');
        console.error('Quick login error:', result.error);
      } else {
        router.push(callbackUrl);
        router.refresh();
      }
    } catch (error) {
      setError('An error occurred. Please try again.');
      console.error('Quick login error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBypassAuth = () => {
    if (isDev) {
      localStorage.setItem('bypassAuth', 'true');
      window.location.href = callbackUrl;
    }
  };

  const clearCache = () => {
    if ('caches' in window) {
      try {
        caches.keys().then((keyList) => {
          return Promise.all(
            keyList.map((key) => {
              return caches.delete(key);
            })
          );
        }).then(() => {
          window.location.reload();
          setError('Cache cleared. Try logging in again.');
        });
      } catch (e) {
        console.error('Failed to clear cache:', e);
        setError('Failed to clear cache. Try refreshing the page.');
      }
    } else {
      setError('Cache API not available in your browser.');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 bg-white p-10 rounded-xl shadow-lg">
        <div className="text-center">
          <div className="flex justify-center">
            <div className="h-16 w-16 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white text-2xl font-bold">TM</div>
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            TaskMaster
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Sign in to your account to manage projects
          </p>
        </div>

        {isDev && (
          <div className="mt-4">
            <button
              onClick={handleQuickLogin}
              disabled={isLoading}
              className="w-full flex justify-center py-2 px-4 border border-amber-300 text-sm font-medium rounded-md text-amber-900 bg-amber-50 hover:bg-amber-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              {isLoading ? 'Logging in...' : 'Quick Development Login'}
            </button>
            <p className="text-xs text-center text-gray-500 mt-1">
              (Uses <EMAIL> / password)
            </p>
          </div>
        )}

        {isDev && (
          <div className="mt-4">
            <button
              onClick={handleBypassAuth}
              className="w-full flex justify-center py-2 px-4 border border-purple-300 text-sm font-medium rounded-md text-purple-900 bg-purple-50 hover:bg-purple-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200"
            >
              Skip Authentication (Dev Mode)
            </button>
            <p className="text-xs text-center text-gray-500 mt-1">
              Bypass authentication check entirely (development only)
            </p>
          </div>
        )}

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="rounded-md -space-y-px">
            <div className="mb-4">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Email address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="Email address"
                defaultValue={isDev ? "<EMAIL>" : ""}
              />
            </div>
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                required
                className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="Password"
                defaultValue={isDev ? "password" : ""}
              />
            </div>
          </div>

          {error && (
            <div className="bg-red-50 text-red-500 p-3 rounded-md text-sm text-center">
              {error}
            </div>
          )}

          {isDev && (
            <div className="bg-amber-50 border border-amber-200 p-3 rounded-md">
              <p className="text-amber-800 text-sm font-medium mb-1">Development Credentials</p>
              <div className="space-y-1">
                <p className="text-amber-700 text-xs"><strong>Option 1:</strong> <EMAIL> / password</p>
                <p className="text-amber-700 text-xs"><strong>Option 2:</strong> <EMAIL> / password</p>
              </div>
              <div className="mt-2 pt-2 border-t border-amber-200 space-y-2">
                <Link
                  href="/auth/setup"
                  className="text-xs text-amber-800 hover:text-amber-900 font-medium flex items-center justify-center"
                >
                  <svg className="h-3 w-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd"></path>
                  </svg>
                  Create admin account automatically
                </Link>
                <Link
                  href="/auth/sync-users"
                  className="text-xs text-amber-800 hover:text-amber-900 font-medium flex items-center justify-center"
                >
                  <svg className="h-3 w-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd"></path>
                  </svg>
                  Sync existing Firestore users
                </Link>
                <Link
                  href="/auth/reset-passwords"
                  className="text-xs text-amber-800 hover:text-amber-900 font-medium flex items-center justify-center"
                >
                  <svg className="h-3 w-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z" clipRule="evenodd"></path>
                  </svg>
                  Reset all user passwords
                </Link>
              </div>
            </div>
          )}

          {isDev && (
            <div className="flex justify-center">
              <button
                type="button"
                onClick={() => setShowTroubleshooting(!showTroubleshooting)}
                className="text-xs text-blue-600 hover:text-blue-800 font-medium"
              >
                {showTroubleshooting ? 'Hide Troubleshooting' : 'Having Issues? Show Troubleshooting'}
              </button>
            </div>
          )}

          {isDev && showTroubleshooting && (
            <div className="bg-blue-50 border border-blue-200 p-3 rounded-md">
              <p className="text-blue-800 text-sm font-medium mb-2">Troubleshooting Options</p>
              <div className="flex flex-col space-y-2">
                <button
                  type="button"
                  onClick={clearCache}
                  className="w-full text-xs text-left py-1.5 px-3 bg-white border border-blue-200 rounded-md hover:bg-blue-50 transition-colors"
                >
                  Clear Application Cache
                </button>
                <button
                  type="button"
                  onClick={() => window.location.href = window.location.origin + '?reset=true'}
                  className="w-full text-xs text-left py-1.5 px-3 bg-white border border-blue-200 rounded-md hover:bg-blue-50 transition-colors"
                >
                  Go to Homepage (Reset State)
                </button>
              </div>
            </div>
          )}

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              {isLoading ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Signing in...
                </span>
              ) : (
                'Sign in'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}