import { getDatabase } from '@/lib/database';
import { v4 as uuidv4 } from 'uuid';

// Department type definition
export interface Department {
  id: string;
  name: string;
  description: string | null;
  budget: number | null;
  createdAt: Date;
  updatedAt: Date;
}

// Department with stats (for UI display)
export interface DepartmentWithStats extends Department {
  memberCount: number;
  projectCount: number;
  totalBudget: number;
}

// Get all departments with stats
export async function getAllDepartments(): Promise<DepartmentWithStats[]> {
  try {
    const db = getDatabase();
    
    const stmt = db.prepare(`
      SELECT 
        d.*,
        COUNT(DISTINCT u.id) as memberCount,
        COUNT(DISTINCT p.id) as projectCount,
        COALESCE(SUM(p.capex + p.opex), 0) as totalBudget
      FROM departments d
      LEFT JOIN users u ON d.id = u.departmentId
      LEFT JOIN projects p ON d.id = p.departmentId
      GROUP BY d.id, d.name, d.description, d.budget, d.createdAt, d.updatedAt
      ORDER BY d.name
    `);
    
    const rows = stmt.all() as any[];
    
    return rows.map(row => ({
      id: row.id,
      name: row.name,
      description: row.description,
      budget: row.budget,
      createdAt: new Date(row.createdAt),
      updatedAt: new Date(row.updatedAt),
      memberCount: row.memberCount || 0,
      projectCount: row.projectCount || 0,
      totalBudget: row.totalBudget || 0
    }));
  } catch (error) {
    console.error('Error getting departments:', error);
    throw error;
  }
}

// Get department by ID
export async function getDepartmentById(id: string): Promise<Department | null> {
  try {
    const db = getDatabase();
    
    const stmt = db.prepare(`
      SELECT * FROM departments WHERE id = ?
    `);
    
    const row = stmt.get(id) as any;
    
    if (!row) {
      return null;
    }
    
    return {
      id: row.id,
      name: row.name,
      description: row.description,
      budget: row.budget,
      createdAt: new Date(row.createdAt),
      updatedAt: new Date(row.updatedAt)
    };
  } catch (error) {
    console.error(`Error getting department ${id}:`, error);
    throw error;
  }
}

// Create new department
export async function createDepartment(departmentData: Omit<Department, 'id' | 'createdAt' | 'updatedAt'>): Promise<Department> {
  try {
    const db = getDatabase();
    const id = uuidv4();
    const now = new Date().toISOString();
    
    const stmt = db.prepare(`
      INSERT INTO departments (id, name, description, budget, createdAt, updatedAt)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    
    stmt.run(
      id,
      departmentData.name,
      departmentData.description,
      departmentData.budget,
      now,
      now
    );
    
    const newDepartment = await getDepartmentById(id);
    if (!newDepartment) {
      throw new Error('Failed to create department');
    }
    
    return newDepartment;
  } catch (error) {
    console.error('Error creating department:', error);
    throw error;
  }
}

// Update department
export async function updateDepartment(id: string, departmentData: Partial<Department>): Promise<Department> {
  try {
    const db = getDatabase();
    const now = new Date().toISOString();
    
    // Build dynamic update query
    const fields = [];
    const values = [];
    
    if (departmentData.name !== undefined) {
      fields.push('name = ?');
      values.push(departmentData.name);
    }
    if (departmentData.description !== undefined) {
      fields.push('description = ?');
      values.push(departmentData.description);
    }
    if (departmentData.budget !== undefined) {
      fields.push('budget = ?');
      values.push(departmentData.budget);
    }
    
    fields.push('updatedAt = ?');
    values.push(now);
    values.push(id);
    
    const stmt = db.prepare(`
      UPDATE departments 
      SET ${fields.join(', ')}
      WHERE id = ?
    `);
    
    stmt.run(...values);
    
    const updatedDepartment = await getDepartmentById(id);
    if (!updatedDepartment) {
      throw new Error(`Department with ID ${id} not found`);
    }
    
    return updatedDepartment;
  } catch (error) {
    console.error(`Error updating department ${id}:`, error);
    throw error;
  }
}

// Delete department
export async function deleteDepartment(id: string): Promise<void> {
  try {
    const db = getDatabase();
    
    // Check if department has any users
    const userCheck = db.prepare('SELECT COUNT(*) as count FROM users WHERE departmentId = ?');
    const userResult = userCheck.get(id) as any;
    
    if (userResult.count > 0) {
      throw new Error('Cannot delete department with associated users');
    }
    
    // Check if department has any projects
    const projectCheck = db.prepare('SELECT COUNT(*) as count FROM projects WHERE departmentId = ?');
    const projectResult = projectCheck.get(id) as any;
    
    if (projectResult.count > 0) {
      throw new Error('Cannot delete department with associated projects');
    }
    
    const stmt = db.prepare('DELETE FROM departments WHERE id = ?');
    const result = stmt.run(id);
    
    if (result.changes === 0) {
      throw new Error(`Department with ID ${id} not found`);
    }
  } catch (error) {
    console.error(`Error deleting department ${id}:`, error);
    throw error;
  }
}

// Check if department name exists
export async function departmentNameExists(name: string, excludeId?: string): Promise<boolean> {
  try {
    const db = getDatabase();
    
    let stmt;
    let result;
    
    if (excludeId) {
      stmt = db.prepare('SELECT COUNT(*) as count FROM departments WHERE name = ? AND id != ?');
      result = stmt.get(name, excludeId) as any;
    } else {
      stmt = db.prepare('SELECT COUNT(*) as count FROM departments WHERE name = ?');
      result = stmt.get(name) as any;
    }
    
    return result.count > 0;
  } catch (error) {
    console.error('Error checking department name:', error);
    throw error;
  }
}

// Get department statistics
export async function getDepartmentStats(id: string): Promise<{
  memberCount: number;
  projectCount: number;
  totalBudget: number;
  activeProjects: number;
  completedProjects: number;
}> {
  try {
    const db = getDatabase();
    
    // Get member count
    const memberStmt = db.prepare('SELECT COUNT(*) as count FROM users WHERE departmentId = ?');
    const memberResult = memberStmt.get(id) as any;
    
    // Get project statistics
    const projectStmt = db.prepare(`
      SELECT 
        COUNT(*) as totalProjects,
        COUNT(CASE WHEN status = 'Completed' THEN 1 END) as completedProjects,
        COUNT(CASE WHEN status != 'Completed' THEN 1 END) as activeProjects,
        COALESCE(SUM(capex + opex), 0) as totalBudget
      FROM projects 
      WHERE departmentId = ?
    `);
    const projectResult = projectStmt.get(id) as any;
    
    return {
      memberCount: memberResult.count || 0,
      projectCount: projectResult.totalProjects || 0,
      totalBudget: projectResult.totalBudget || 0,
      activeProjects: projectResult.activeProjects || 0,
      completedProjects: projectResult.completedProjects || 0
    };
  } catch (error) {
    console.error(`Error getting department stats for ${id}:`, error);
    throw error;
  }
}
