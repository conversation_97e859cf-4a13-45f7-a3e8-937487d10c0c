import { NextRequest, NextResponse } from 'next/server';
import { getProjectFileById, deleteProjectFile, getFileContent } from '@/services/sqlite/fileService';
import { logFileDelete } from '@/services/sqlite/projectLogService';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// GET /api/projects/[id]/files/[fileId] - Download a file
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string; fileId: string } }
) {
  try {
    const { fileId } = params;
    
    const { file, content } = await getFileContent(fileId);
    
    // Set appropriate headers for file download
    const headers = new Headers();
    headers.set('Content-Type', file.mimeType);
    headers.set('Content-Disposition', `attachment; filename="${file.originalName}"`);
    headers.set('Content-Length', content.length.toString());
    
    return new NextResponse(content, { headers });
  } catch (error) {
    console.error('Error downloading file:', error);
    return NextResponse.json(
      { error: 'File not found' },
      { status: 404 }
    );
  }
}

// DELETE /api/projects/[id]/files/[fileId] - Delete a file
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; fileId: string } }
) {
  try {
    const { id: projectId, fileId } = params;
    const session = await getServerSession(authOptions);
    
    // Get file info before deletion
    const file = await getProjectFileById(fileId);
    if (!file) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }
    
    // Delete file
    await deleteProjectFile(fileId);
    
    // Log file deletion
    await logFileDelete(
      projectId,
      file.originalName,
      session?.user?.id,
      session?.user?.name
    );
    
    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error('Error deleting file:', error);
    return NextResponse.json(
      { error: 'Failed to delete file' },
      { status: 500 }
    );
  }
}
