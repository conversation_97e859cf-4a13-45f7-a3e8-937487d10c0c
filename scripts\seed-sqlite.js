// Script to seed SQLite database with sample data
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
const path = require('path');
const fs = require('fs');
const Database = require('better-sqlite3');

// Load environment variables from .env.local
function loadEnvFile() {
  const envPath = path.join(process.cwd(), '.env.local');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');

    lines.forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          const value = valueParts.join('=');
          process.env[key.trim()] = value.trim();
        }
      }
    });

    console.log('✅ Loaded environment variables from .env.local');
  }
}

// Load environment variables
loadEnvFile();

// Configuration for database and file paths
const CONFIG = {
  SHARED_PATH: process.env.SHARED_PATH || 'Z:/ProjectPlatform',
  DATABASE_NAME: 'data.sqlite',
  UPLOADS_FOLDER: 'uploads'
};

const PATHS = {
  DATABASE: path.join(CONFIG.SHARED_PATH, CONFIG.DATABASE_NAME),
  UPLOADS: path.join(CONFIG.SHARED_PATH, CONFIG.UPLOADS_FOLDER)
};

let db = null;

function getDatabase() {
  if (!db) {
    db = new Database(PATHS.DATABASE);
    db.pragma('foreign_keys = ON');
  }
  return db;
}

// Service functions
async function createDepartment(departmentData) {
  const database = getDatabase();
  const id = uuidv4();
  const now = new Date().toISOString();

  const stmt = database.prepare(`
    INSERT INTO departments (id, name, description, budget, createdAt, updatedAt)
    VALUES (?, ?, ?, ?, ?, ?)
  `);

  stmt.run(id, departmentData.name, departmentData.description, departmentData.budget, now, now);

  const getStmt = database.prepare('SELECT * FROM departments WHERE id = ?');
  const row = getStmt.get(id);

  return {
    id: row.id,
    name: row.name,
    description: row.description,
    budget: row.budget,
    createdAt: new Date(row.createdAt),
    updatedAt: new Date(row.updatedAt)
  };
}

async function createUser(userData) {
  const database = getDatabase();
  const id = uuidv4();
  const now = new Date().toISOString();

  const stmt = database.prepare(`
    INSERT INTO users (
      id, name, email, password, role, department, departmentId,
      phone, bio, jobTitle, createdAt, updatedAt
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);

  stmt.run(
    id, userData.name, userData.email, userData.password, userData.role,
    userData.department, userData.departmentId, userData.phone, userData.bio,
    userData.jobTitle, now, now
  );

  const getStmt = database.prepare('SELECT * FROM users WHERE id = ?');
  const row = getStmt.get(id);

  return {
    id: row.id,
    name: row.name,
    email: row.email,
    password: row.password,
    role: row.role,
    department: row.department,
    departmentId: row.departmentId,
    phone: row.phone,
    bio: row.bio,
    jobTitle: row.jobTitle,
    createdAt: new Date(row.createdAt),
    updatedAt: new Date(row.updatedAt)
  };
}

async function createProject(projectData) {
  const database = getDatabase();
  const id = uuidv4();
  const now = new Date().toISOString();

  const stmt = database.prepare(`
    INSERT INTO projects (
      id, projectTitle, drivers, type, year, opdFocal, capex, opex, status, subStatus,
      department, departmentId, area, awardedCompany, savings, percentage,
      startDate, endDate, dateOfReceiveFinalDoc, quarterOfYear, column1,
      createdBy, statusChangeNote, statusChangeDate, createdAt, updatedAt
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);

  stmt.run(
    id, projectData.projectTitle, projectData.drivers, projectData.type, projectData.year,
    projectData.opdFocal, projectData.capex, projectData.opex, projectData.status, projectData.subStatus,
    projectData.department, projectData.departmentId, projectData.area, projectData.awardedCompany,
    projectData.savings, projectData.percentage, projectData.startDate?.toISOString(),
    projectData.endDate?.toISOString(), projectData.dateOfReceiveFinalDoc?.toISOString(),
    projectData.quarterOfYear, projectData.column1, projectData.createdBy, projectData.statusChangeNote,
    projectData.statusChangeDate?.toISOString(), now, now
  );

  const getStmt = database.prepare('SELECT * FROM projects WHERE id = ?');
  const row = getStmt.get(id);

  return {
    id: row.id,
    projectTitle: row.projectTitle,
    drivers: row.drivers,
    type: row.type,
    year: row.year,
    opdFocal: row.opdFocal,
    capex: row.capex,
    opex: row.opex,
    status: row.status,
    subStatus: row.subStatus,
    department: row.department,
    departmentId: row.departmentId,
    area: row.area,
    awardedCompany: row.awardedCompany,
    savings: row.savings,
    percentage: row.percentage,
    quarterOfYear: row.quarterOfYear,
    createdBy: row.createdBy,
    createdAt: new Date(row.createdAt),
    updatedAt: new Date(row.updatedAt)
  };
}

async function logProjectCreation(projectId, projectTitle, userId, userName) {
  const database = getDatabase();
  const id = uuidv4();
  const now = new Date().toISOString();

  const stmt = database.prepare(`
    INSERT INTO project_logs (
      id, projectId, action, field, oldValue, newValue, note, userId, userName, createdAt
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);

  stmt.run(
    id, projectId, 'PROJECT_CREATED', null, null, projectTitle,
    `Project "${projectTitle}" was created`, userId, userName, now
  );
}

// Sample data
const DEPARTMENTS = [
  {
    name: 'Engineering',
    description: 'Product development and technical innovation',
    budget: 2500000
  },
  {
    name: 'Marketing',
    description: 'Brand management and customer acquisition',
    budget: 1200000
  },
  {
    name: 'Operations',
    description: 'Business operations and process optimization',
    budget: 800000
  },
  {
    name: 'Finance',
    description: 'Financial planning and analysis',
    budget: 600000
  },
  {
    name: 'Human Resources',
    description: 'Talent management and organizational development',
    budget: 400000
  }
];

const USERS = [
  {
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'ADMIN',
    department: 'Engineering',
    jobTitle: 'System Administrator'
  },
  {
    name: 'John Smith',
    email: '<EMAIL>',
    password: 'password123',
    role: 'PMO',
    department: 'Engineering',
    jobTitle: 'Project Manager'
  },
  {
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    password: 'password123',
    role: 'USER',
    department: 'Marketing',
    jobTitle: 'Marketing Specialist'
  },
  {
    name: 'Mike Davis',
    email: '<EMAIL>',
    password: 'password123',
    role: 'USER',
    department: 'Operations',
    jobTitle: 'Operations Manager'
  },
  {
    name: 'Emily Chen',
    email: '<EMAIL>',
    password: 'password123',
    role: 'USER',
    department: 'Finance',
    jobTitle: 'Financial Analyst'
  }
];

const PROJECTS = [
  {
    projectTitle: 'Customer Portal Redesign',
    drivers: 'Improve user experience and increase customer satisfaction',
    type: 'Digital Transformation',
    year: 2024,
    opdFocal: 'John Smith',
    capex: 150000,
    opex: 50000,
    status: 'In Progress',
    subStatus: 'Development Phase',
    department: 'Engineering',
    area: 'Technology',
    awardedCompany: 'TechSolutions Inc',
    savings: 75000,
    percentage: 65,
    quarterOfYear: 'Q1'
  },
  {
    projectTitle: 'Marketing Automation Platform',
    drivers: 'Streamline marketing processes and improve lead generation',
    type: 'Process Improvement',
    year: 2024,
    opdFocal: 'Sarah Johnson',
    capex: 80000,
    opex: 30000,
    status: 'Planning',
    subStatus: 'Requirements Gathering',
    department: 'Marketing',
    area: 'Marketing',
    awardedCompany: 'MarketingTech Ltd',
    savings: 45000,
    percentage: 25,
    quarterOfYear: 'Q2'
  },
  {
    projectTitle: 'Supply Chain Optimization',
    drivers: 'Reduce costs and improve delivery times',
    type: 'Operational Excellence',
    year: 2024,
    opdFocal: 'Mike Davis',
    capex: 200000,
    opex: 75000,
    status: 'Completed',
    subStatus: 'Closed',
    department: 'Operations',
    area: 'Operations',
    awardedCompany: 'LogisticsPro',
    savings: 120000,
    percentage: 100,
    quarterOfYear: 'Q4'
  },
  {
    projectTitle: 'Financial Reporting Dashboard',
    drivers: 'Improve financial visibility and decision making',
    type: 'Analytics & Reporting',
    year: 2024,
    opdFocal: 'Emily Chen',
    capex: 60000,
    opex: 20000,
    status: 'In Progress',
    subStatus: 'Testing Phase',
    department: 'Finance',
    area: 'Finance',
    awardedCompany: 'DataViz Solutions',
    savings: 35000,
    percentage: 80,
    quarterOfYear: 'Q3'
  },
  {
    projectTitle: 'Employee Training Platform',
    drivers: 'Enhance employee skills and reduce training costs',
    type: 'Human Capital',
    year: 2024,
    opdFocal: 'John Smith',
    capex: 40000,
    opex: 15000,
    status: 'Planning',
    subStatus: 'Vendor Selection',
    department: 'Human Resources',
    area: 'HR',
    awardedCompany: 'LearnTech Corp',
    savings: 25000,
    percentage: 15,
    quarterOfYear: 'Q2'
  }
];

async function seedDatabase() {
  console.log('🌱 Seeding SQLite database with sample data...\n');

  try {
    // Check if database exists
    console.log('📊 Checking database...');
    if (!fs.existsSync(PATHS.DATABASE)) {
      console.log('❌ Database not found. Please run "npm run init:db" first.');
      process.exit(1);
    }

    // Initialize database connection
    getDatabase();
    console.log('✅ Database connected');

    // Create departments
    console.log('\n🏢 Creating departments...');
    const createdDepartments = {};

    for (const deptData of DEPARTMENTS) {
      try {
        const department = await createDepartment(deptData);
        createdDepartments[department.name] = department;
        console.log(`✅ Created department: ${department.name}`);
      } catch (error) {
        if (error.message.includes('UNIQUE constraint failed')) {
          console.log(`⚠️  Department already exists: ${deptData.name}`);
        } else {
          throw error;
        }
      }
    }

    // Create users
    console.log('\n👥 Creating users...');
    const createdUsers = {};

    for (const userData of USERS) {
      try {
        const hashedPassword = await bcrypt.hash(userData.password, 10);
        const department = createdDepartments[userData.department];

        const user = await createUser({
          ...userData,
          password: hashedPassword,
          departmentId: department?.id || null
        });

        createdUsers[user.name] = user;
        console.log(`✅ Created user: ${user.name} (${user.email})`);
      } catch (error) {
        if (error.message.includes('UNIQUE constraint failed')) {
          console.log(`⚠️  User already exists: ${userData.email}`);
        } else {
          throw error;
        }
      }
    }

    // Create projects
    console.log('\n📋 Creating projects...');

    for (const projectData of PROJECTS) {
      try {
        const department = createdDepartments[projectData.department];
        const user = createdUsers[projectData.opdFocal];

        const project = await createProject({
          ...projectData,
          departmentId: department?.id || '',
          createdBy: user?.id || 'system'
        });

        // Log project creation
        await logProjectCreation(
          project.id,
          project.projectTitle,
          user?.id,
          user?.name
        );

        console.log(`✅ Created project: ${project.projectTitle}`);
      } catch (error) {
        console.error(`❌ Failed to create project: ${projectData.projectTitle}`, error.message);
      }
    }

    console.log('\n🎉 Database seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   Departments: ${Object.keys(createdDepartments).length}`);
    console.log(`   Users: ${Object.keys(createdUsers).length}`);
    console.log(`   Projects: ${PROJECTS.length}`);

    console.log('\n🔑 Default Admin Credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123');

    console.log('\n🚀 Ready to start the application!');
    console.log('   Run "npm run dev" for development server');
    console.log('   Run "npm run electron:dev" for Electron app');

  } catch (error) {
    console.error('\n❌ Database seeding failed:', error);
    process.exit(1);
  }
}

// Run seeding
seedDatabase();
