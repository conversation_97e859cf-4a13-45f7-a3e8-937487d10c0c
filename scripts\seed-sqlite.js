// Script to seed SQLite database with sample data
const bcrypt = require('bcryptjs');

// Import services (in production these would be compiled .js files)
const { initializeDatabase } = require('../src/lib/database.ts');
const { createDepartment } = require('../src/services/sqlite/departmentService.ts');
const { createUser } = require('../src/services/sqlite/userService.ts');
const { createProject } = require('../src/services/sqlite/projectService.ts');
const { logProjectCreation } = require('../src/services/sqlite/projectLogService.ts');

// Sample data
const DEPARTMENTS = [
  {
    name: 'Engineering',
    description: 'Product development and technical innovation',
    budget: 2500000
  },
  {
    name: 'Marketing',
    description: 'Brand management and customer acquisition',
    budget: 1200000
  },
  {
    name: 'Operations',
    description: 'Business operations and process optimization',
    budget: 800000
  },
  {
    name: 'Finance',
    description: 'Financial planning and analysis',
    budget: 600000
  },
  {
    name: 'Human Resources',
    description: 'Talent management and organizational development',
    budget: 400000
  }
];

const USERS = [
  {
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'ADMIN',
    department: 'Engineering',
    jobTitle: 'System Administrator'
  },
  {
    name: 'John Smith',
    email: '<EMAIL>',
    password: 'password123',
    role: 'PMO',
    department: 'Engineering',
    jobTitle: 'Project Manager'
  },
  {
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    password: 'password123',
    role: 'USER',
    department: 'Marketing',
    jobTitle: 'Marketing Specialist'
  },
  {
    name: 'Mike Davis',
    email: '<EMAIL>',
    password: 'password123',
    role: 'USER',
    department: 'Operations',
    jobTitle: 'Operations Manager'
  },
  {
    name: 'Emily Chen',
    email: '<EMAIL>',
    password: 'password123',
    role: 'USER',
    department: 'Finance',
    jobTitle: 'Financial Analyst'
  }
];

const PROJECTS = [
  {
    projectTitle: 'Customer Portal Redesign',
    drivers: 'Improve user experience and increase customer satisfaction',
    type: 'Digital Transformation',
    year: 2024,
    opdFocal: 'John Smith',
    capex: 150000,
    opex: 50000,
    status: 'In Progress',
    subStatus: 'Development Phase',
    department: 'Engineering',
    area: 'Technology',
    awardedCompany: 'TechSolutions Inc',
    savings: 75000,
    percentage: 65,
    quarterOfYear: 'Q1'
  },
  {
    projectTitle: 'Marketing Automation Platform',
    drivers: 'Streamline marketing processes and improve lead generation',
    type: 'Process Improvement',
    year: 2024,
    opdFocal: 'Sarah Johnson',
    capex: 80000,
    opex: 30000,
    status: 'Planning',
    subStatus: 'Requirements Gathering',
    department: 'Marketing',
    area: 'Marketing',
    awardedCompany: 'MarketingTech Ltd',
    savings: 45000,
    percentage: 25,
    quarterOfYear: 'Q2'
  },
  {
    projectTitle: 'Supply Chain Optimization',
    drivers: 'Reduce costs and improve delivery times',
    type: 'Operational Excellence',
    year: 2024,
    opdFocal: 'Mike Davis',
    capex: 200000,
    opex: 75000,
    status: 'Completed',
    subStatus: 'Closed',
    department: 'Operations',
    area: 'Operations',
    awardedCompany: 'LogisticsPro',
    savings: 120000,
    percentage: 100,
    quarterOfYear: 'Q4'
  },
  {
    projectTitle: 'Financial Reporting Dashboard',
    drivers: 'Improve financial visibility and decision making',
    type: 'Analytics & Reporting',
    year: 2024,
    opdFocal: 'Emily Chen',
    capex: 60000,
    opex: 20000,
    status: 'In Progress',
    subStatus: 'Testing Phase',
    department: 'Finance',
    area: 'Finance',
    awardedCompany: 'DataViz Solutions',
    savings: 35000,
    percentage: 80,
    quarterOfYear: 'Q3'
  },
  {
    projectTitle: 'Employee Training Platform',
    drivers: 'Enhance employee skills and reduce training costs',
    type: 'Human Capital',
    year: 2024,
    opdFocal: 'John Smith',
    capex: 40000,
    opex: 15000,
    status: 'Planning',
    subStatus: 'Vendor Selection',
    department: 'Human Resources',
    area: 'HR',
    awardedCompany: 'LearnTech Corp',
    savings: 25000,
    percentage: 15,
    quarterOfYear: 'Q2'
  }
];

async function seedDatabase() {
  console.log('🌱 Seeding SQLite database with sample data...\n');
  
  try {
    // Initialize database
    console.log('📊 Initializing database...');
    initializeDatabase();
    console.log('✅ Database initialized');
    
    // Create departments
    console.log('\n🏢 Creating departments...');
    const createdDepartments = {};
    
    for (const deptData of DEPARTMENTS) {
      try {
        const department = await createDepartment(deptData);
        createdDepartments[department.name] = department;
        console.log(`✅ Created department: ${department.name}`);
      } catch (error) {
        if (error.message.includes('UNIQUE constraint failed')) {
          console.log(`⚠️  Department already exists: ${deptData.name}`);
        } else {
          throw error;
        }
      }
    }
    
    // Create users
    console.log('\n👥 Creating users...');
    const createdUsers = {};
    
    for (const userData of USERS) {
      try {
        const hashedPassword = await bcrypt.hash(userData.password, 10);
        const department = createdDepartments[userData.department];
        
        const user = await createUser({
          ...userData,
          password: hashedPassword,
          departmentId: department?.id || null
        });
        
        createdUsers[user.name] = user;
        console.log(`✅ Created user: ${user.name} (${user.email})`);
      } catch (error) {
        if (error.message.includes('UNIQUE constraint failed')) {
          console.log(`⚠️  User already exists: ${userData.email}`);
        } else {
          throw error;
        }
      }
    }
    
    // Create projects
    console.log('\n📋 Creating projects...');
    
    for (const projectData of PROJECTS) {
      try {
        const department = createdDepartments[projectData.department];
        const user = createdUsers[projectData.opdFocal];
        
        const project = await createProject({
          ...projectData,
          departmentId: department?.id || '',
          createdBy: user?.id || 'system'
        });
        
        // Log project creation
        await logProjectCreation(
          project.id,
          project.projectTitle,
          user?.id,
          user?.name
        );
        
        console.log(`✅ Created project: ${project.projectTitle}`);
      } catch (error) {
        console.error(`❌ Failed to create project: ${projectData.projectTitle}`, error.message);
      }
    }
    
    console.log('\n🎉 Database seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   Departments: ${Object.keys(createdDepartments).length}`);
    console.log(`   Users: ${Object.keys(createdUsers).length}`);
    console.log(`   Projects: ${PROJECTS.length}`);
    
    console.log('\n🔑 Default Admin Credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123');
    
    console.log('\n🚀 Ready to start the application!');
    console.log('   Run "npm run dev" for development server');
    console.log('   Run "npm run electron:dev" for Electron app');
    
  } catch (error) {
    console.error('\n❌ Database seeding failed:', error);
    process.exit(1);
  }
}

// Run seeding
seedDatabase();
