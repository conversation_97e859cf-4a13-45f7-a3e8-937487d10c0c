// Script to initialize SQLite database
const path = require('path');
const fs = require('fs');
const Database = require('better-sqlite3');

// Configuration for database and file paths
const CONFIG = {
  // Default shared network path - can be overridden by environment variable
  SHARED_PATH: process.env.SHARED_PATH || 'Z:/ProjectPlatform',
  DATABASE_NAME: 'data.sqlite',
  UPLOADS_FOLDER: 'uploads'
};

// Get full paths
const PATHS = {
  DATABASE: path.join(CONFIG.SHARED_PATH, CONFIG.DATABASE_NAME),
  UPLOADS: path.join(CONFIG.SHARED_PATH, CONFIG.UPLOADS_FOLDER)
};

let db = null;

// Initialize database connection
function initializeDatabase() {
  if (db) {
    return db;
  }

  try {
    // Ensure the shared directory exists
    if (!fs.existsSync(CONFIG.SHARED_PATH)) {
      throw new Error(`Shared folder not found: ${CONFIG.SHARED_PATH}`);
    }

    // Ensure uploads directory exists
    if (!fs.existsSync(PATHS.UPLOADS)) {
      fs.mkdirSync(PATHS.UPLOADS, { recursive: true });
    }

    // Initialize SQLite database
    db = new Database(PATHS.DATABASE);

    // Enable foreign keys
    db.pragma('foreign_keys = ON');

    // Create tables if they don't exist
    createTables(db);

    console.log(`Database initialized at: ${PATHS.DATABASE}`);
    return db;
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
}

// Create all necessary tables
function createTables(database) {
  // Users table
  database.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      email TEXT UNIQUE NOT NULL,
      password TEXT,
      role TEXT DEFAULT 'USER',
      department TEXT,
      departmentId TEXT,
      phone TEXT,
      bio TEXT,
      jobTitle TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (departmentId) REFERENCES departments(id) ON DELETE SET NULL
    )
  `);

  // Departments table
  database.exec(`
    CREATE TABLE IF NOT EXISTS departments (
      id TEXT PRIMARY KEY,
      name TEXT UNIQUE NOT NULL,
      description TEXT,
      budget REAL,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Projects table
  database.exec(`
    CREATE TABLE IF NOT EXISTS projects (
      id TEXT PRIMARY KEY,
      projectTitle TEXT NOT NULL,
      drivers TEXT,
      type TEXT,
      year INTEGER,
      opdFocal TEXT,
      capex REAL,
      opex REAL,
      status TEXT DEFAULT 'Planning',
      subStatus TEXT,
      department TEXT,
      departmentId TEXT,
      area TEXT,
      awardedCompany TEXT,
      savings REAL,
      percentage INTEGER DEFAULT 0,
      startDate DATETIME,
      endDate DATETIME,
      dateOfReceiveFinalDoc DATETIME,
      quarterOfYear TEXT DEFAULT 'Q1',
      column1 TEXT,
      createdBy TEXT,
      statusChangeNote TEXT,
      statusChangeDate DATETIME,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (departmentId) REFERENCES departments(id) ON DELETE SET NULL
    )
  `);

  // Project logs table
  database.exec(`
    CREATE TABLE IF NOT EXISTS project_logs (
      id TEXT PRIMARY KEY,
      projectId TEXT NOT NULL,
      action TEXT NOT NULL,
      field TEXT,
      oldValue TEXT,
      newValue TEXT,
      note TEXT,
      userId TEXT,
      userName TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (projectId) REFERENCES projects(id) ON DELETE CASCADE
    )
  `);

  // Project files table
  database.exec(`
    CREATE TABLE IF NOT EXISTS project_files (
      id TEXT PRIMARY KEY,
      projectId TEXT NOT NULL,
      filename TEXT NOT NULL,
      originalName TEXT NOT NULL,
      filePath TEXT NOT NULL,
      fileSize INTEGER NOT NULL,
      mimeType TEXT NOT NULL,
      uploadedBy TEXT,
      uploadedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (projectId) REFERENCES projects(id) ON DELETE CASCADE
    )
  `);

  // Create indexes for better performance
  database.exec(`
    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
    CREATE INDEX IF NOT EXISTS idx_users_department ON users(departmentId);
    CREATE INDEX IF NOT EXISTS idx_projects_department ON projects(departmentId);
    CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
    CREATE INDEX IF NOT EXISTS idx_project_logs_project ON project_logs(projectId);
    CREATE INDEX IF NOT EXISTS idx_project_files_project ON project_files(projectId);
  `);

  console.log('Database tables created successfully');
}

// Health check function
function checkDatabaseHealth() {
  try {
    // Check if shared folder exists
    if (!fs.existsSync(CONFIG.SHARED_PATH)) {
      return {
        status: 'error',
        message: `Shared folder not found: ${CONFIG.SHARED_PATH}`,
        paths: PATHS
      };
    }

    // Check if database file exists and is accessible
    const database = initializeDatabase();
    const result = database.prepare('SELECT 1 as test').get();

    return {
      status: 'healthy',
      message: 'Database connection successful',
      paths: PATHS
    };
  } catch (error) {
    return {
      status: 'error',
      message: `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      paths: PATHS
    };
  }
}

async function initializeApp() {
  console.log('🚀 Initializing Project Management App Database...\n');

  try {
    // Check if shared path exists
    console.log(`📁 Checking shared path: ${CONFIG.SHARED_PATH}`);

    if (!fs.existsSync(CONFIG.SHARED_PATH)) {
      console.log(`❌ Shared folder not found: ${CONFIG.SHARED_PATH}`);
      console.log('\n💡 Please ensure the shared network folder exists or update the SHARED_PATH environment variable.');
      console.log('   Example: set SHARED_PATH=C:\\ProjectPlatform (for local testing)');
      process.exit(1);
    }

    console.log('✅ Shared folder found');

    // Initialize database
    console.log('\n📊 Initializing SQLite database...');
    const db = initializeDatabase();
    console.log('✅ Database initialized successfully');

    // Check database health
    console.log('\n🔍 Checking database health...');
    const health = checkDatabaseHealth();

    if (health.status === 'healthy') {
      console.log('✅ Database health check passed');
    } else {
      console.log(`❌ Database health check failed: ${health.message}`);
      process.exit(1);
    }

    // Display configuration
    console.log('\n📋 Configuration:');
    console.log(`   Database Path: ${PATHS.DATABASE}`);
    console.log(`   Uploads Path: ${PATHS.UPLOADS}`);
    console.log(`   Shared Path: ${CONFIG.SHARED_PATH}`);

    console.log('\n🎉 Database initialization completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('   1. Run "npm run seed:sqlite" to populate with sample data');
    console.log('   2. Run "npm run dev" to start the development server');
    console.log('   3. Run "npm run electron:dev" to start the Electron app');

  } catch (error) {
    console.error('\n❌ Database initialization failed:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('   1. Ensure the shared folder exists and is accessible');
    console.error('   2. Check file permissions');
    console.error('   3. Verify the SHARED_PATH environment variable');
    process.exit(1);
  }
}

// Run initialization
initializeApp();
