// <PERSON><PERSON><PERSON> to initialize SQLite database
const path = require('path');
const fs = require('fs');

// Import the database initialization function
// Note: We need to use require with .ts files in a Node.js context
// For production, this would be compiled to .js
const { initializeDatabase, checkDatabaseHealth, CONFIG, PATHS } = require('../src/lib/database.ts');

async function initializeApp() {
  console.log('🚀 Initializing Project Management App Database...\n');
  
  try {
    // Check if shared path exists
    console.log(`📁 Checking shared path: ${CONFIG.SHARED_PATH}`);
    
    if (!fs.existsSync(CONFIG.SHARED_PATH)) {
      console.log(`❌ Shared folder not found: ${CONFIG.SHARED_PATH}`);
      console.log('\n💡 Please ensure the shared network folder exists or update the SHARED_PATH environment variable.');
      console.log('   Example: set SHARED_PATH=C:\\ProjectPlatform (for local testing)');
      process.exit(1);
    }
    
    console.log('✅ Shared folder found');
    
    // Initialize database
    console.log('\n📊 Initializing SQLite database...');
    const db = initializeDatabase();
    console.log('✅ Database initialized successfully');
    
    // Check database health
    console.log('\n🔍 Checking database health...');
    const health = checkDatabaseHealth();
    
    if (health.status === 'healthy') {
      console.log('✅ Database health check passed');
    } else {
      console.log(`❌ Database health check failed: ${health.message}`);
      process.exit(1);
    }
    
    // Display configuration
    console.log('\n📋 Configuration:');
    console.log(`   Database Path: ${PATHS.DATABASE}`);
    console.log(`   Uploads Path: ${PATHS.UPLOADS}`);
    console.log(`   Shared Path: ${CONFIG.SHARED_PATH}`);
    
    console.log('\n🎉 Database initialization completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('   1. Run "npm run seed:sqlite" to populate with sample data');
    console.log('   2. Run "npm run dev" to start the development server');
    console.log('   3. Run "npm run electron:dev" to start the Electron app');
    
  } catch (error) {
    console.error('\n❌ Database initialization failed:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('   1. Ensure the shared folder exists and is accessible');
    console.error('   2. Check file permissions');
    console.error('   3. Verify the SHARED_PATH environment variable');
    process.exit(1);
  }
}

// Run initialization
initializeApp();
