/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  // Only use export for production builds
  ...(process.env.NODE_ENV === 'production' && {
    output: 'export',
    trailingSlash: true,
  }),
  images: {
    unoptimized: true
  },
  webpack: (config, { isServer }) => {
    // Handle better-sqlite3 for Electron
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
      };
    }

    // Exclude better-sqlite3 from client bundle
    config.externals = config.externals || [];
    config.externals.push('better-sqlite3');

    return config;
  },
  serverExternalPackages: ['better-sqlite3']
};

module.exports = nextConfig;