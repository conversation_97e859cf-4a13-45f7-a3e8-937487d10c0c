import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { db, auth } from './firebase';
import { signInWithEmailAndPassword, createUserWithEmailAndPassword } from 'firebase/auth';
import { doc, getDoc, setDoc } from 'firebase/firestore';

declare module 'next-auth' {
  interface User {
    role?: string;
  }

  interface Session {
    user: {
      id?: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      role?: string;
    }
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role?: string;
  }
}

// Special case for development mode
const handleDevAuth = (email: string, password: string) => {
  if (process.env.NODE_ENV !== 'production') {
    // Default admin credentials
    if (email === '<EMAIL>' && password === 'password') {
      return {
        id: 'admin',
        name: 'Admin User',
        email: '<EMAIL>',
        role: 'ADMIN'
      };
    }

    // Additional development user for AOPS
    if (email === '<EMAIL>' && password === 'password') {
      return {
        id: 'aops-admin',
        name: 'AOPS Admin',
        email: '<EMAIL>',
        role: 'ADMIN'
      };
    }
  }
  return null;
};

export const authOptions: NextAuthOptions = {
  secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-here',
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'text' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        // First check for dev credentials
        const devUser = handleDevAuth(credentials.email, credentials.password);
        if (devUser) return devUser;

        try {
          // Sign in with Firebase Auth
          const userCredential = await signInWithEmailAndPassword(
            auth,
            credentials.email,
            credentials.password
          );

          const uid = userCredential.user.uid;

          // Get additional user data from Firestore
          const userDoc = await getDoc(doc(db, 'users', uid));

          if (!userDoc.exists()) {
            // Create a basic user record if it doesn't exist
            const userData = {
              name: credentials.email.split('@')[0],
              email: credentials.email,
              role: 'USER',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            };

            await setDoc(doc(db, 'users', uid), userData);

            return {
              id: uid,
              email: credentials.email,
              name: userData.name,
              role: userData.role
            };
          }

          const userData = userDoc.data();

          return {
            id: uid,
            email: credentials.email,
            name: userData.name || credentials.email.split('@')[0],
            role: userData.role || 'USER'
          };
        } catch (error) {
          console.error('Firebase auth error:', error);

          // For development, create a fallback admin user
          if (process.env.NODE_ENV !== 'production' &&
              ((credentials.email === '<EMAIL>' && credentials.password === 'password') ||
               (credentials.email === '<EMAIL>' && credentials.password === 'password'))) {

            try {
              // Try to create the admin user in Firebase
              await createUserWithEmailAndPassword(auth, credentials.email, credentials.password)
                .then(async (userCredential) => {
                  const uid = userCredential.user.uid;

                  // Create admin user in Firestore
                  const userName = credentials.email === '<EMAIL>' ? 'AOPS Admin' : 'Admin User';
                  await setDoc(doc(db, 'users', uid), {
                    name: userName,
                    email: credentials.email,
                    role: 'ADMIN',
                    department: 'Admin',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                  });
                })
                .catch(err => {
                  console.log('Admin user may already exist:', err);
                });

              const userName = credentials.email === '<EMAIL>' ? 'AOPS Admin' : 'Admin User';
              const userId = credentials.email === '<EMAIL>' ? 'aops-admin' : 'admin';
              return {
                id: userId,
                name: userName,
                email: credentials.email,
                role: 'ADMIN'
              };
            } catch (createError) {
              console.error('Could not create admin user:', createError);
              return null;
            }
          }

          return null;
        }
      }
    })
  ],
  callbacks: {
    jwt: async ({ token, user }) => {
      if (user) {
        token.role = user.role;
        token.sub = user.id;
      }
      return token;
    },
    session: async ({ session, token }) => {
      if (token && session.user) {
        session.user.role = token.role;
        session.user.id = token.sub;
      }
      return session;
    }
  },
  pages: {
    signIn: '/auth/signin',
    signOut: '/auth/signin',
    error: '/auth/error',
  },
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  debug: process.env.NODE_ENV === 'development',
};