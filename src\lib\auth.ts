import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { getUserByEmail } from '@/services/sqlite/userService';
import bcrypt from 'bcryptjs';

declare module 'next-auth' {
  interface User {
    role?: string;
  }

  interface Session {
    user: {
      id?: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      role?: string;
    }
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role?: string;
  }
}

// Special case for development mode
const handleDevAuth = (email: string, password: string) => {
  if (process.env.NODE_ENV !== 'production') {
    // Default admin credentials
    if (email === '<EMAIL>' && password === 'password') {
      return {
        id: 'admin',
        name: 'Admin User',
        email: '<EMAIL>',
        role: 'ADMIN'
      };
    }

    // Additional development user for AOPS
    if (email === '<EMAIL>' && password === 'password') {
      return {
        id: 'aops-admin',
        name: 'AOPS Admin',
        email: '<EMAIL>',
        role: 'ADMIN'
      };
    }
  }
  return null;
};

export const authOptions: NextAuthOptions = {
  secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-here',
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'text' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        // First check for dev credentials
        const devUser = handleDevAuth(credentials.email, credentials.password);
        if (devUser) return devUser;

        try {
          // Get user from SQLite database
          const user = await getUserByEmail(credentials.email);

          if (!user) {
            console.log('User not found:', credentials.email);
            return null;
          }

          // Check password
          if (!user.password) {
            console.log('User has no password set:', credentials.email);
            return null;
          }

          const isValidPassword = await bcrypt.compare(credentials.password, user.password);

          if (!isValidPassword) {
            console.log('Invalid password for user:', credentials.email);
            return null;
          }

          return {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role
          };
        } catch (error) {
          console.error('SQLite auth error:', error);

          // For development, return fallback admin user if database is not accessible
          if (process.env.NODE_ENV !== 'production' &&
              ((credentials.email === '<EMAIL>' && credentials.password === 'password') ||
               (credentials.email === '<EMAIL>' && credentials.password === 'password') ||
               (credentials.email === '<EMAIL>' && credentials.password === 'admin123'))) {

            const userName = credentials.email === '<EMAIL>' ? 'AOPS Admin' :
                           credentials.email === '<EMAIL>' ? 'Admin User' : 'Admin User';
            const userId = credentials.email === '<EMAIL>' ? 'aops-admin' :
                          credentials.email === '<EMAIL>' ? 'admin' : 'admin';

            return {
              id: userId,
              name: userName,
              email: credentials.email,
              role: 'ADMIN'
            };
          }

          return null;
        }
      }
    })
  ],
  callbacks: {
    jwt: async ({ token, user }) => {
      if (user) {
        token.role = user.role;
        token.sub = user.id;
      }
      return token;
    },
    session: async ({ session, token }) => {
      if (token && session.user) {
        session.user.role = token.role;
        session.user.id = token.sub;
      }
      return session;
    }
  },
  pages: {
    signIn: '/auth/signin',
    signOut: '/auth/signin',
    error: '/auth/error',
  },
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  debug: process.env.NODE_ENV === 'development',
};