import { NextResponse } from 'next/server';
import { checkDatabaseHealth } from '@/lib/database';
import { getProjectStats } from '@/services/sqlite/projectService';
import { getFileStats } from '@/services/sqlite/fileService';

// GET /api/health - Check application health
export async function GET() {
  try {
    // Check database health
    const dbHealth = checkDatabaseHealth();
    
    if (dbHealth.status !== 'healthy') {
      return NextResponse.json({
        status: 'unhealthy',
        database: dbHealth,
        timestamp: new Date().toISOString()
      }, { status: 503 });
    }
    
    // Get basic statistics
    const [projectStats, fileStats] = await Promise.all([
      getProjectStats().catch(() => ({ total: 0, byStatus: {}, byDepartment: {}, totalBudget: 0, totalSavings: 0 })),
      getFileStats().catch(() => ({ totalFiles: 0, totalSize: 0, filesByProject: {}, filesByType: {} }))
    ]);
    
    return NextResponse.json({
      status: 'healthy',
      database: dbHealth,
      statistics: {
        projects: projectStats,
        files: fileStats
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Health check failed:', error);
    return NextResponse.json({
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 503 });
  }
}
