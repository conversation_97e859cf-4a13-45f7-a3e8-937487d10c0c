import { NextRequest, NextResponse } from 'next/server';
import { getAllLogs } from '@/services/sqlite/projectLogService';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// GET /api/logs - Get all project logs
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Only authenticated users can access logs
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get all project logs
    const rawLogs = await getAllLogs();

    // Transform logs to match frontend expectations
    const transformedLogs = rawLogs.map(log => ({
      id: log.id,
      projectId: log.projectId,
      action: log.action,
      description: log.note || `${log.action} action performed`,
      changes: log.field && log.oldValue !== null && log.newValue !== null ? {
        [log.field]: {
          from: log.oldValue,
          to: log.newValue
        }
      } : {},
      note: log.note,
      createdBy: log.userId || 'system',
      createdByName: log.userName || 'System',
      createdAt: log.createdAt.toISOString()
    }));

    return NextResponse.json(transformedLogs);
  } catch (error) {
    console.error('Error fetching all project logs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch project logs' },
      { status: 500 }
    );
  }
}