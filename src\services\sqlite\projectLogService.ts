import { getDatabase } from '@/lib/database';
import { v4 as uuidv4 } from 'uuid';

// Project log type definition
export interface ProjectLog {
  id: string;
  projectId: string;
  action: string;
  field: string | null;
  oldValue: string | null;
  newValue: string | null;
  note: string | null;
  userId: string | null;
  userName: string | null;
  createdAt: Date;
}

// Helper function to convert database row to ProjectLog
function rowToProjectLog(row: any): ProjectLog {
  return {
    id: row.id,
    projectId: row.projectId,
    action: row.action,
    field: row.field,
    oldValue: row.oldValue,
    newValue: row.newValue,
    note: row.note,
    userId: row.userId,
    userName: row.userName,
    createdAt: new Date(row.createdAt)
  };
}

// Get all logs for a project
export async function getProjectLogs(projectId: string): Promise<ProjectLog[]> {
  try {
    const db = getDatabase();
    
    const stmt = db.prepare(`
      SELECT * FROM project_logs 
      WHERE projectId = ?
      ORDER BY createdAt DESC
    `);
    
    const rows = stmt.all(projectId) as any[];
    return rows.map(rowToProjectLog);
  } catch (error) {
    console.error(`Error getting project logs for ${projectId}:`, error);
    throw error;
  }
}

// Create a new project log
export async function createProjectLog(logData: Omit<ProjectLog, 'id' | 'createdAt'>): Promise<ProjectLog> {
  try {
    const db = getDatabase();
    const id = uuidv4();
    const now = new Date().toISOString();
    
    const stmt = db.prepare(`
      INSERT INTO project_logs (
        id, projectId, action, field, oldValue, newValue, note, userId, userName, createdAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    stmt.run(
      id,
      logData.projectId,
      logData.action,
      logData.field,
      logData.oldValue,
      logData.newValue,
      logData.note,
      logData.userId,
      logData.userName,
      now
    );
    
    const newLog = await getProjectLogById(id);
    if (!newLog) {
      throw new Error('Failed to create project log');
    }
    
    return newLog;
  } catch (error) {
    console.error('Error creating project log:', error);
    throw error;
  }
}

// Get project log by ID
export async function getProjectLogById(id: string): Promise<ProjectLog | null> {
  try {
    const db = getDatabase();
    
    const stmt = db.prepare('SELECT * FROM project_logs WHERE id = ?');
    const row = stmt.get(id) as any;
    
    if (!row) {
      return null;
    }
    
    return rowToProjectLog(row);
  } catch (error) {
    console.error(`Error getting project log ${id}:`, error);
    throw error;
  }
}

// Delete a project log
export async function deleteProjectLog(id: string): Promise<void> {
  try {
    const db = getDatabase();
    
    const stmt = db.prepare('DELETE FROM project_logs WHERE id = ?');
    const result = stmt.run(id);
    
    if (result.changes === 0) {
      throw new Error(`Project log with ID ${id} not found`);
    }
  } catch (error) {
    console.error(`Error deleting project log ${id}:`, error);
    throw error;
  }
}

// Delete all logs for a project
export async function deleteAllProjectLogs(projectId: string): Promise<void> {
  try {
    const db = getDatabase();
    
    const stmt = db.prepare('DELETE FROM project_logs WHERE projectId = ?');
    stmt.run(projectId);
  } catch (error) {
    console.error(`Error deleting all project logs for ${projectId}:`, error);
    throw error;
  }
}

// Get all logs (for admin/analytics)
export async function getAllLogs(limit?: number): Promise<ProjectLog[]> {
  try {
    const db = getDatabase();
    
    let query = 'SELECT * FROM project_logs ORDER BY createdAt DESC';
    if (limit) {
      query += ` LIMIT ${limit}`;
    }
    
    const stmt = db.prepare(query);
    const rows = stmt.all() as any[];
    return rows.map(rowToProjectLog);
  } catch (error) {
    console.error('Error getting all logs:', error);
    throw error;
  }
}

// Convenience functions for common log types
export async function logProjectCreation(projectId: string, projectTitle: string, userId?: string, userName?: string): Promise<ProjectLog> {
  return createProjectLog({
    projectId,
    action: 'PROJECT_CREATED',
    field: null,
    oldValue: null,
    newValue: projectTitle,
    note: `Project "${projectTitle}" was created`,
    userId: userId || null,
    userName: userName || null
  });
}

export async function logStatusChange(
  projectId: string, 
  oldStatus: string, 
  newStatus: string, 
  note?: string,
  userId?: string, 
  userName?: string
): Promise<ProjectLog> {
  return createProjectLog({
    projectId,
    action: 'STATUS_CHANGED',
    field: 'status',
    oldValue: oldStatus,
    newValue: newStatus,
    note: note || `Status changed from "${oldStatus}" to "${newStatus}"`,
    userId: userId || null,
    userName: userName || null
  });
}

export async function logSubStatusChange(
  projectId: string, 
  oldSubStatus: string, 
  newSubStatus: string, 
  note?: string,
  userId?: string, 
  userName?: string
): Promise<ProjectLog> {
  return createProjectLog({
    projectId,
    action: 'SUB_STATUS_CHANGED',
    field: 'subStatus',
    oldValue: oldSubStatus,
    newValue: newSubStatus,
    note: note || `Sub-status changed from "${oldSubStatus}" to "${newSubStatus}"`,
    userId: userId || null,
    userName: userName || null
  });
}

export async function logProjectUpdate(
  projectId: string, 
  field: string, 
  oldValue: string, 
  newValue: string, 
  userId?: string, 
  userName?: string
): Promise<ProjectLog> {
  return createProjectLog({
    projectId,
    action: 'PROJECT_UPDATED',
    field,
    oldValue,
    newValue,
    note: `${field} changed from "${oldValue}" to "${newValue}"`,
    userId: userId || null,
    userName: userName || null
  });
}

export async function logFileUpload(
  projectId: string, 
  fileName: string, 
  userId?: string, 
  userName?: string
): Promise<ProjectLog> {
  return createProjectLog({
    projectId,
    action: 'FILE_UPLOADED',
    field: 'files',
    oldValue: null,
    newValue: fileName,
    note: `File "${fileName}" was uploaded`,
    userId: userId || null,
    userName: userName || null
  });
}

export async function logFileDelete(
  projectId: string, 
  fileName: string, 
  userId?: string, 
  userName?: string
): Promise<ProjectLog> {
  return createProjectLog({
    projectId,
    action: 'FILE_DELETED',
    field: 'files',
    oldValue: fileName,
    newValue: null,
    note: `File "${fileName}" was deleted`,
    userId: userId || null,
    userName: userName || null
  });
}
