import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

// Configuration for database and file paths
export const CONFIG = {
  // Default shared network path - can be overridden by environment variable
  SHARED_PATH: process.env.SHARED_PATH || 'Z:/ProjectPlatform',
  DATABASE_NAME: 'data.sqlite',
  UPLOADS_FOLDER: 'uploads'
};

// Get full paths
export const PATHS = {
  DATABASE: path.join(CONFIG.SHARED_PATH, CONFIG.DATABASE_NAME),
  UPLOADS: path.join(CONFIG.SHARED_PATH, CONFIG.UPLOADS_FOLDER)
};

let db: Database.Database | null = null;

// Initialize database connection
export function initializeDatabase(): Database.Database {
  if (db) {
    return db;
  }

  try {
    // Ensure the shared directory exists
    if (!fs.existsSync(CONFIG.SHARED_PATH)) {
      throw new Error(`Shared folder not found: ${CONFIG.SHARED_PATH}`);
    }

    // Ensure uploads directory exists
    if (!fs.existsSync(PATHS.UPLOADS)) {
      fs.mkdirSync(PATHS.UPLOADS, { recursive: true });
    }

    // Initialize SQLite database
    db = new Database(PATHS.DATABASE);
    
    // Enable foreign keys
    db.pragma('foreign_keys = ON');
    
    // Create tables if they don't exist
    createTables(db);
    
    console.log(`Database initialized at: ${PATHS.DATABASE}`);
    return db;
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
}

// Get database instance
export function getDatabase(): Database.Database {
  if (!db) {
    return initializeDatabase();
  }
  return db;
}

// Create all necessary tables
function createTables(database: Database.Database) {
  // Users table
  database.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      email TEXT UNIQUE NOT NULL,
      password TEXT,
      role TEXT DEFAULT 'USER',
      department TEXT,
      departmentId TEXT,
      phone TEXT,
      bio TEXT,
      jobTitle TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (departmentId) REFERENCES departments(id) ON DELETE SET NULL
    )
  `);

  // Departments table
  database.exec(`
    CREATE TABLE IF NOT EXISTS departments (
      id TEXT PRIMARY KEY,
      name TEXT UNIQUE NOT NULL,
      description TEXT,
      budget REAL,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Projects table
  database.exec(`
    CREATE TABLE IF NOT EXISTS projects (
      id TEXT PRIMARY KEY,
      projectTitle TEXT NOT NULL,
      drivers TEXT,
      type TEXT,
      year INTEGER,
      opdFocal TEXT,
      capex REAL,
      opex REAL,
      status TEXT DEFAULT 'Planning',
      subStatus TEXT,
      department TEXT,
      departmentId TEXT,
      area TEXT,
      awardedCompany TEXT,
      savings REAL,
      percentage INTEGER DEFAULT 0,
      startDate DATETIME,
      endDate DATETIME,
      dateOfReceiveFinalDoc DATETIME,
      quarterOfYear TEXT DEFAULT 'Q1',
      column1 TEXT,
      createdBy TEXT,
      statusChangeNote TEXT,
      statusChangeDate DATETIME,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (departmentId) REFERENCES departments(id) ON DELETE SET NULL
    )
  `);

  // Project logs table
  database.exec(`
    CREATE TABLE IF NOT EXISTS project_logs (
      id TEXT PRIMARY KEY,
      projectId TEXT NOT NULL,
      action TEXT NOT NULL,
      field TEXT,
      oldValue TEXT,
      newValue TEXT,
      note TEXT,
      userId TEXT,
      userName TEXT,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (projectId) REFERENCES projects(id) ON DELETE CASCADE
    )
  `);

  // Project files table (NEW)
  database.exec(`
    CREATE TABLE IF NOT EXISTS project_files (
      id TEXT PRIMARY KEY,
      projectId TEXT NOT NULL,
      filename TEXT NOT NULL,
      originalName TEXT NOT NULL,
      filePath TEXT NOT NULL,
      fileSize INTEGER NOT NULL,
      mimeType TEXT NOT NULL,
      uploadedBy TEXT,
      uploadedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (projectId) REFERENCES projects(id) ON DELETE CASCADE
    )
  `);

  // Create indexes for better performance
  database.exec(`
    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
    CREATE INDEX IF NOT EXISTS idx_users_department ON users(departmentId);
    CREATE INDEX IF NOT EXISTS idx_projects_department ON projects(departmentId);
    CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
    CREATE INDEX IF NOT EXISTS idx_project_logs_project ON project_logs(projectId);
    CREATE INDEX IF NOT EXISTS idx_project_files_project ON project_files(projectId);
  `);

  console.log('Database tables created successfully');
}

// Close database connection
export function closeDatabase() {
  if (db) {
    db.close();
    db = null;
  }
}

// Health check function
export function checkDatabaseHealth(): { status: string; message: string; paths: typeof PATHS } {
  try {
    // Check if shared folder exists
    if (!fs.existsSync(CONFIG.SHARED_PATH)) {
      return {
        status: 'error',
        message: `Shared folder not found: ${CONFIG.SHARED_PATH}`,
        paths: PATHS
      };
    }

    // Check if database file exists and is accessible
    const database = getDatabase();
    const result = database.prepare('SELECT 1 as test').get();
    
    return {
      status: 'healthy',
      message: 'Database connection successful',
      paths: PATHS
    };
  } catch (error) {
    return {
      status: 'error',
      message: `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      paths: PATHS
    };
  }
}
