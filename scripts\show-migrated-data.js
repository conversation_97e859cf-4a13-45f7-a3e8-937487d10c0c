// Script to show sample migrated data
const Database = require('better-sqlite3');
const path = require('path');

// Configuration - use local path for this script
const PATHS = {
  DATABASE: path.join(process.cwd(), 'local-data', 'data.sqlite')
};

function showMigratedData() {
  console.log('🔍 Showing sample migrated data from Firebase...\n');

  try {
    const db = new Database(PATHS.DATABASE);

    console.log('📊 MIGRATION SUMMARY:');
    const deptCount = db.prepare('SELECT COUNT(*) as count FROM departments').get().count;
    const userCount = db.prepare('SELECT COUNT(*) as count FROM users').get().count;
    const projectCount = db.prepare('SELECT COUNT(*) as count FROM projects').get().count;
    const logCount = db.prepare('SELECT COUNT(*) as count FROM project_logs').get().count;

    console.log(`   Departments: ${deptCount}`);
    console.log(`   Users: ${userCount}`);
    console.log(`   Projects: ${projectCount}`);
    console.log(`   Logs: ${logCount}`);

    console.log('\n🏢 DEPARTMENTS:');
    const departments = db.prepare('SELECT name, description, budget FROM departments ORDER BY name').all();
    departments.forEach(d => {
      console.log(`   • ${d.name}`);
      if (d.description) console.log(`     Description: ${d.description}`);
      if (d.budget) console.log(`     Budget: $${d.budget.toLocaleString()}`);
    });

    console.log('\n👥 USERS (Sample):');
    const users = db.prepare('SELECT name, email, role, department FROM users ORDER BY name LIMIT 8').all();
    users.forEach(u => {
      console.log(`   • ${u.name} (${u.email})`);
      console.log(`     Role: ${u.role} | Department: ${u.department || 'None'}`);
    });

    console.log('\n📋 PROJECTS (Sample):');
    const projects = db.prepare(`
      SELECT projectTitle, status, department, capex, opex, savings
      FROM projects
      ORDER BY projectTitle
      LIMIT 8
    `).all();
    projects.forEach(p => {
      console.log(`   • ${p.projectTitle}`);
      console.log(`     Status: ${p.status} | Department: ${p.department}`);
      if (p.capex || p.opex) {
        console.log(`     Budget: CAPEX $${(p.capex || 0).toLocaleString()}, OPEX $${(p.opex || 0).toLocaleString()}`);
      }
      if (p.savings) {
        console.log(`     Savings: $${p.savings.toLocaleString()}`);
      }
    });

    console.log('\n📝 RECENT LOGS (Sample):');
    const logs = db.prepare(`
      SELECT action, note, userName, createdAt
      FROM project_logs
      ORDER BY createdAt DESC
      LIMIT 5
    `).all();
    logs.forEach(l => {
      const date = new Date(l.createdAt).toLocaleDateString();
      console.log(`   • ${l.action} by ${l.userName || 'System'} on ${date}`);
      if (l.note) console.log(`     Note: ${l.note}`);
    });

    console.log('\n✅ All data successfully migrated from Firebase to SQLite!');
    console.log('🚀 You can now use the application with your real data.');

    db.close();

  } catch (error) {
    console.error('❌ Error reading migrated data:', error);
  }
}

showMigratedData();
