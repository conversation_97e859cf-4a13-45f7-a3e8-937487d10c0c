import { NextRequest, NextResponse } from 'next/server';
import { getProjectFiles, saveProjectFile, validateFile } from '@/services/sqlite/fileService';
import { logFileUpload } from '@/services/sqlite/projectLogService';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// GET /api/projects/[id]/files - Get all files for a project
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const files = await getProjectFiles(id);
    return NextResponse.json(files);
  } catch (error) {
    console.error('Error fetching project files:', error);
    return NextResponse.json(
      { error: 'Failed to fetch project files' },
      { status: 500 }
    );
  }
}

// POST /api/projects/[id]/files - Upload files to a project
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id: projectId } = params;
    const session = await getServerSession(authOptions);
    
    // Get form data
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    
    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }
    
    const uploadedFiles = [];
    const errors = [];
    
    for (const file of files) {
      try {
        // Validate file
        const validation = validateFile({
          mimetype: file.type,
          size: file.size
        });
        
        if (!validation.valid) {
          errors.push(`${file.name}: ${validation.error}`);
          continue;
        }
        
        // Convert File to buffer
        const buffer = Buffer.from(await file.arrayBuffer());
        
        // Save file
        const savedFile = await saveProjectFile(
          projectId,
          {
            buffer,
            originalname: file.name,
            mimetype: file.type,
            size: file.size
          },
          session?.user?.id
        );
        
        // Log file upload
        await logFileUpload(
          projectId,
          file.name,
          session?.user?.id,
          session?.user?.name
        );
        
        uploadedFiles.push(savedFile);
      } catch (error) {
        console.error(`Error uploading file ${file.name}:`, error);
        errors.push(`${file.name}: Upload failed`);
      }
    }
    
    return NextResponse.json({
      uploadedFiles,
      errors,
      success: uploadedFiles.length > 0
    });
  } catch (error) {
    console.error('Error uploading project files:', error);
    return NextResponse.json(
      { error: 'Failed to upload files' },
      { status: 500 }
    );
  }
}
