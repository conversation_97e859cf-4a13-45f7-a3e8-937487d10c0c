import { getDatabase } from '@/lib/database';
import { v4 as uuidv4 } from 'uuid';

// User type definition
export interface User {
  id: string;
  name: string;
  email: string;
  password?: string;
  role: string;
  department: string | null;
  departmentId: string | null;
  phone?: string;
  bio?: string;
  jobTitle?: string;
  createdAt: Date;
  updatedAt: Date;
  departmentName?: string;
}

// Get all users
export async function getAllUsers(): Promise<User[]> {
  try {
    const db = getDatabase();
    
    const stmt = db.prepare(`
      SELECT 
        u.*,
        d.name as departmentName
      FROM users u
      LEFT JOIN departments d ON u.departmentId = d.id
      ORDER BY u.name
    `);
    
    const rows = stmt.all() as any[];
    
    return rows.map(row => ({
      id: row.id,
      name: row.name,
      email: row.email,
      password: row.password,
      role: row.role,
      department: row.department,
      departmentId: row.departmentId,
      phone: row.phone,
      bio: row.bio,
      jobTitle: row.jobTitle,
      createdAt: new Date(row.createdAt),
      updatedAt: new Date(row.updatedAt),
      departmentName: row.departmentName
    }));
  } catch (error) {
    console.error('Error getting users:', error);
    throw error;
  }
}

// Get user by ID
export async function getUserById(id: string): Promise<User | null> {
  try {
    const db = getDatabase();
    
    const stmt = db.prepare(`
      SELECT 
        u.*,
        d.name as departmentName
      FROM users u
      LEFT JOIN departments d ON u.departmentId = d.id
      WHERE u.id = ?
    `);
    
    const row = stmt.get(id) as any;
    
    if (!row) {
      return null;
    }
    
    return {
      id: row.id,
      name: row.name,
      email: row.email,
      password: row.password,
      role: row.role,
      department: row.department,
      departmentId: row.departmentId,
      phone: row.phone,
      bio: row.bio,
      jobTitle: row.jobTitle,
      createdAt: new Date(row.createdAt),
      updatedAt: new Date(row.updatedAt),
      departmentName: row.departmentName
    };
  } catch (error) {
    console.error(`Error getting user ${id}:`, error);
    throw error;
  }
}

// Get user by email
export async function getUserByEmail(email: string): Promise<User | null> {
  try {
    const db = getDatabase();
    
    const stmt = db.prepare(`
      SELECT 
        u.*,
        d.name as departmentName
      FROM users u
      LEFT JOIN departments d ON u.departmentId = d.id
      WHERE u.email = ?
    `);
    
    const row = stmt.get(email) as any;
    
    if (!row) {
      return null;
    }
    
    return {
      id: row.id,
      name: row.name,
      email: row.email,
      password: row.password,
      role: row.role,
      department: row.department,
      departmentId: row.departmentId,
      phone: row.phone,
      bio: row.bio,
      jobTitle: row.jobTitle,
      createdAt: new Date(row.createdAt),
      updatedAt: new Date(row.updatedAt),
      departmentName: row.departmentName
    };
  } catch (error) {
    console.error(`Error getting user by email ${email}:`, error);
    throw error;
  }
}

// Create new user
export async function createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
  try {
    const db = getDatabase();
    const id = uuidv4();
    const now = new Date().toISOString();
    
    const stmt = db.prepare(`
      INSERT INTO users (
        id, name, email, password, role, department, departmentId, 
        phone, bio, jobTitle, createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    stmt.run(
      id,
      userData.name,
      userData.email,
      userData.password,
      userData.role,
      userData.department,
      userData.departmentId,
      userData.phone,
      userData.bio,
      userData.jobTitle,
      now,
      now
    );
    
    const newUser = await getUserById(id);
    if (!newUser) {
      throw new Error('Failed to create user');
    }
    
    return newUser;
  } catch (error) {
    console.error('Error creating user:', error);
    throw error;
  }
}

// Update user
export async function updateUser(id: string, userData: Partial<User>): Promise<User> {
  try {
    const db = getDatabase();
    const now = new Date().toISOString();
    
    // Build dynamic update query
    const fields = [];
    const values = [];
    
    if (userData.name !== undefined) {
      fields.push('name = ?');
      values.push(userData.name);
    }
    if (userData.email !== undefined) {
      fields.push('email = ?');
      values.push(userData.email);
    }
    if (userData.password !== undefined) {
      fields.push('password = ?');
      values.push(userData.password);
    }
    if (userData.role !== undefined) {
      fields.push('role = ?');
      values.push(userData.role);
    }
    if (userData.department !== undefined) {
      fields.push('department = ?');
      values.push(userData.department);
    }
    if (userData.departmentId !== undefined) {
      fields.push('departmentId = ?');
      values.push(userData.departmentId);
    }
    if (userData.phone !== undefined) {
      fields.push('phone = ?');
      values.push(userData.phone);
    }
    if (userData.bio !== undefined) {
      fields.push('bio = ?');
      values.push(userData.bio);
    }
    if (userData.jobTitle !== undefined) {
      fields.push('jobTitle = ?');
      values.push(userData.jobTitle);
    }
    
    fields.push('updatedAt = ?');
    values.push(now);
    values.push(id);
    
    const stmt = db.prepare(`
      UPDATE users 
      SET ${fields.join(', ')}
      WHERE id = ?
    `);
    
    stmt.run(...values);
    
    const updatedUser = await getUserById(id);
    if (!updatedUser) {
      throw new Error(`User with ID ${id} not found`);
    }
    
    return updatedUser;
  } catch (error) {
    console.error(`Error updating user ${id}:`, error);
    throw error;
  }
}

// Delete user
export async function deleteUser(id: string): Promise<void> {
  try {
    const db = getDatabase();
    
    const stmt = db.prepare('DELETE FROM users WHERE id = ?');
    const result = stmt.run(id);
    
    if (result.changes === 0) {
      throw new Error(`User with ID ${id} not found`);
    }
  } catch (error) {
    console.error(`Error deleting user ${id}:`, error);
    throw error;
  }
}

// Get users by department
export async function getUsersByDepartment(departmentId: string): Promise<User[]> {
  try {
    const db = getDatabase();
    
    const stmt = db.prepare(`
      SELECT 
        u.*,
        d.name as departmentName
      FROM users u
      LEFT JOIN departments d ON u.departmentId = d.id
      WHERE u.departmentId = ?
      ORDER BY u.name
    `);
    
    const rows = stmt.all(departmentId) as any[];
    
    return rows.map(row => ({
      id: row.id,
      name: row.name,
      email: row.email,
      password: row.password,
      role: row.role,
      department: row.department,
      departmentId: row.departmentId,
      phone: row.phone,
      bio: row.bio,
      jobTitle: row.jobTitle,
      createdAt: new Date(row.createdAt),
      updatedAt: new Date(row.updatedAt),
      departmentName: row.departmentName
    }));
  } catch (error) {
    console.error(`Error getting users by department ${departmentId}:`, error);
    throw error;
  }
}

// Get users by role
export async function getUsersByRole(role: 'USER' | 'ADMIN' | 'PMO'): Promise<User[]> {
  try {
    const db = getDatabase();
    
    const stmt = db.prepare(`
      SELECT 
        u.*,
        d.name as departmentName
      FROM users u
      LEFT JOIN departments d ON u.departmentId = d.id
      WHERE u.role = ?
      ORDER BY u.name
    `);
    
    const rows = stmt.all(role) as any[];
    
    return rows.map(row => ({
      id: row.id,
      name: row.name,
      email: row.email,
      password: row.password,
      role: row.role,
      department: row.department,
      departmentId: row.departmentId,
      phone: row.phone,
      bio: row.bio,
      jobTitle: row.jobTitle,
      createdAt: new Date(row.createdAt),
      updatedAt: new Date(row.updatedAt),
      departmentName: row.departmentName
    }));
  } catch (error) {
    console.error(`Error getting users by role ${role}:`, error);
    throw error;
  }
}
